@echo off
echo ============================================================
echo 正在启动视频处理程序（虚拟环境 + GPU兼容性模式）
echo 使用Python 3.8虚拟环境，解决TensorFlow兼容性问题
echo ============================================================

REM 设置GPU兼容性环境变量
echo 设置GPU兼容性环境变量...
set TF_FORCE_GPU_ALLOW_GROWTH=true
set TF_GPU_ALLOCATOR=cuda_malloc_async
set CUDA_MODULE_LOADING=LAZY
set TF_CUDA_COMPUTE_CAPABILITIES=8.6
set TF_GPU_THREAD_MODE=gpu_private
set TF_GPU_THREAD_COUNT=2
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=0
set TF_DISABLE_MKL=1

REM 确保不强制使用CPU
set CUDA_VISIBLE_DEVICES=

echo ✅ GPU兼容性环境变量设置完成
echo 🚀 使用Python 3.8虚拟环境启动程序...
echo.

REM 使用虚拟环境中的Python
venv38\Scripts\python main.py

echo.
echo 程序执行完成
pause
