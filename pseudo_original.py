import os
import sys
import subprocess
import random
import time
import re
import traceback
import tempfile
import shutil
import numpy as np
from scipy import signal
# 导入替代音高调整函数
from new_alternative_pitch_shift import smart_pitch_shift, apply_pitch_shift

# 兼容层，保持原有代码调用不变
def no_loss_pitch_shift(y, sr, n_steps, log_func=None):
    """
    无损音高调整方案 - 使用替代算法实现
    
    此函数是一个兼容层，实际调用new_alternative_pitch_shift.py中的实现
    使用线性插值方法处理小幅调整，避免回音问题
    """
    if log_func:
        log_func(f"[无损音高调整] 调整值: {n_steps}半音，使用替代算法")
    
    # 直接调用smart_pitch_shift函数
    return smart_pitch_shift(y, sr, n_steps, log_func)

def run_subprocess(*args, **kwargs):
    """包装subprocess.run，在Windows上隐藏命令行窗口"""
    if sys.platform.startswith('win') and 'creationflags' not in kwargs:
        kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
    return subprocess.run(*args, **kwargs)

def popen_subprocess(*args, **kwargs):
    """包装subprocess.Popen，在Windows上隐藏命令行窗口"""
    if sys.platform.startswith('win') and 'creationflags' not in kwargs:
        kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
    return subprocess.Popen(*args, **kwargs)

from cover_generator import generate_cover_with_cgpy
from status_manager import StatusManager, update_feature_status, reset_feature_status, output_feature_summary, status_manager, StepStatus
from step_exception_handler import step_exception_handler, StepException, InputValidationError, OutputValidationError, MissingStreamError, ProcessExecutionError, ResourceError, step_try_except

# 获取总步骤数常量
TOTAL_STEPS = StatusManager.TOTAL_STEPS

# 为了向后兼容，确保feature_status变量可用
# 这样即使代码中直接访问feature_status也不会出错
feature_status = status_manager.feature_status

# 全局变量：存储第2步插帧数量，供第4步使用
step2_insert_frames = 0

def prepare_watermark_files(watermark_text):
    """
    准备水印文件并获取安全路径

    Args:
        watermark_text: 水印文本内容

    Returns:
        tuple: (font_path, text_path, font_path_for_ffmpeg, text_path_for_ffmpeg, temp_dir)
    """
    import config_manager
    import tempfile

    try:
        config = config_manager.get_config()

        # 获取字体路径 - 使用更兼容的字体选择策略
        font_path = None

        # 尝试使用系统字体，按优先级排序
        font_candidates = [
            "C:/Windows/Fonts/arial.ttf",      # Arial - 最兼容
            "C:/Windows/Fonts/simhei.ttf",     # 黑体 - 支持中文
            "C:/Windows/Fonts/msyh.ttc",       # 微软雅黑
        ]

        for candidate in font_candidates:
            if os.path.exists(candidate):
                font_path = candidate
                break

        # 如果系统字体都不存在，使用项目字体
        if not font_path:
            project_font = config.get('font_path', 'SourceHanSansSC-Medium.otf')
            font_path = os.path.join(os.path.dirname(__file__), 'fonts', project_font)

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='watermark_')

        # 文本路径（如果需要的话）
        text_path = None

        # 为FFmpeg准备安全的路径格式
        # Windows路径需要转换为FFmpeg可识别的格式
        # 对于drawtext滤镜，简单地转换为正斜杠格式
        font_path_for_ffmpeg = font_path.replace('\\', '/')
        text_path_for_ffmpeg = None

        return font_path, text_path, font_path_for_ffmpeg, text_path_for_ffmpeg, temp_dir

    except Exception as e:
        # 如果出错，返回默认值 - 使用最兼容的Arial字体
        font_path = "C:/Windows/Fonts/arial.ttf"
        text_path = None
        font_path_for_ffmpeg = font_path.replace('\\', '/')
        text_path_for_ffmpeg = None
        temp_dir = tempfile.mkdtemp(prefix='watermark_')

        return font_path, text_path, font_path_for_ffmpeg, text_path_for_ffmpeg, temp_dir

def ensure_font_available(font_name='SourceHanSansSC-Medium.otf', log_func=None):
    """
    确保字体文件在temp_fonts目录中可用，避免路径中有空格导致的问题
    
    Args:
        font_name: 字体文件名
        log_func: 日志记录函数
    
    Returns:
        str: 安全的字体文件路径（无空格）
    """
    try:
        # 源字体路径
        source_font_path = os.path.join(os.path.dirname(__file__), 'fonts', font_name)
        
        # 创建临时字体目录
        temp_fonts_dir = os.path.join(os.path.dirname(__file__), 'temp_fonts')
        os.makedirs(temp_fonts_dir, exist_ok=True)
        
        # 目标字体路径
        target_font_path = os.path.join(temp_fonts_dir, font_name)
        
        # 如果目标字体不存在或大小为0，则复制
        if not os.path.exists(target_font_path) or os.path.getsize(target_font_path) == 0:
            if os.path.exists(source_font_path):
                shutil.copy2(source_font_path, target_font_path)
                if log_func:
                    log_func(f"[字体] 已复制字体文件到临时目录: {target_font_path}")
            else:
                if log_func:
                    log_func(f"[字体] 警告: 源字体文件不存在: {source_font_path}")
                # 尝试使用系统字体
                return "C:/Windows/Fonts/simhei.ttf"
        
        # 返回安全的字体路径（转换为正斜杠格式）
        return target_font_path.replace('\\', '/')
    
    except Exception as e:
        if log_func:
            log_func(f"[字体] 错误: {str(e)}")
        # 出错时返回系统字体
        return "C:/Windows/Fonts/simhei.ttf"

def get_video_resolution(video_path):
    """
    获取视频文件的分辨率

    Args:
        video_path: 视频文件路径

    Returns:
        tuple: (width, height) 视频的宽度和高度
    """
    try:
        cmd_resolution = [
            'ffprobe',
            '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=p=0:s=x',
            video_path
        ]
        result_resolution = subprocess.run(cmd_resolution, capture_output=True, text=True, encoding='utf-8', errors='replace', creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
        if result_resolution.returncode == 0:
            resolution_str = result_resolution.stdout.strip()
            width_str, height_str = resolution_str.split('x')
            width, height = int(width_str), int(height_str)
            return width, height
        else:
            # 如果ffprobe失败，返回默认分辨率
            return 1920, 1080
    except Exception as e:
        # 如果出现任何异常，返回默认分辨率
        return 1920, 1080

# 定义获取媒体流信息的统一函数
def get_media_streams(file_path, check_video_only=False, log_callback=None):
    """获取媒体文件的流信息
    
    Args:
        file_path: 媒体文件路径
        check_video_only: 是否只检查视频流存在性
        log_callback: 日志回调函数
        
    Returns:
        如果check_video_only=True，返回bool值表示是否存在视频流
        否则返回所有流类型的列表
    """
    try:
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'stream=codec_type',
            '-of', 'csv=p=0',
            file_path
        ]
        proc_result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
        
        if proc_result.returncode != 0:
            if log_callback:
                log_callback(f"[视频处理] 获取流信息失败: ffprobe返回非零值")
            return False if check_video_only else []
            
        streams = proc_result.stdout.strip().split('\n') if proc_result.stdout.strip() else []
        
        if check_video_only:
            return 'video' in streams
        else:
            return streams
            
    except Exception as e:
        if log_callback:
            log_callback(f"[视频处理] 获取流信息失败: {str(e)}")
        return False if check_video_only else []

# 检查视频流是否存在
def check_video_streams(file_path, log_callback=None):
    return get_media_streams(file_path, check_video_only=True, log_callback=log_callback)
    
# 获取所有流类型
def get_stream_types(file_path, log_callback=None):
    return get_media_streams(file_path, check_video_only=False, log_callback=log_callback)

# 状态管理已经移至status_manager模块，从那里导入相关函数和对象

# 文件验证工具函数，用于在伪原创步骤之间验证文件


def verify_step_transition(input_path, output_path, step_from, step_to, step_name, result_obj):
    """
    验证伪原创步骤之间的输入输出文件

    Args:
        input_path: 输入文件路径
        output_path: 输出文件路径
        step_from: 来源步骤序号
        step_to: 目标步骤序号
        step_name: 步骤名称
        result_obj: 结果对象（可能被修改）

    Returns:
        bool: 是否可以继续
    """
    try:
        # 验证输入文件
        is_valid_input, input_info = step_exception_handler.verify_step_input(
            [input_path], None, step_to, step_name, result_obj)
        
        if not is_valid_input:
            # 验证失败，已由异常处理器处理
            return False

        # 如果输出文件已存在，验证其有效性（可能是之前处理的残留文件）
        if os.path.exists(output_path):
            try:
                # 尝试删除已有文件，避免使用之前的残留文件
                status_manager.log_detail(
                    f"[伪原创{step_to}] 发现已存在的输出文件，尝试删除: {output_path}")
                os.remove(output_path)
            except Exception as e:
                # 使用专门的资源错误异常
                raise ResourceError(
                    f"无法删除已存在的输出文件，可能被其他程序锁定", 
                    step_to,
                    details=str(e)
                )

        # 记录文件转换信息
        status_manager.log_detail(f"[伪原创{step_to}] 输入文件信息:")
        status_manager.log_detail(
            f"[伪原创{step_to}]   - 尺寸: {input_info['resolution']}")
        status_manager.log_detail(
            f"[伪原创{step_to}]   - 时长: {input_info['duration']:.2f}秒")
        status_manager.log_detail(
            f"[伪原创{step_to}]   - 编码: {input_info['video_codec']}")

        return True
        
    except StepException as e:
        # 处理步骤异常
        step_exception_handler.handle_exception(e, step_to, step_name, result_obj)
        return False
    except Exception as e:
        # 处理其他异常
        error = ProcessExecutionError(
            f"步骤转换验证时出错: {str(e)}", 
            step_to,
            details=traceback.format_exc()
        )
        step_exception_handler.handle_exception(error, step_to, step_name, result_obj)
        return False




def generate_particle_effect(particle_type="snow", duration=10, intensity=0.3):
    """
    生成粒子效果滤镜
    particle_type: snow(雪花), rain(雨滴), sparkle(光点), dust(灰尘)
    duration: 效果持续时间(秒)
    intensity: 效果强度 0.1-1.0
    """
    effects = {
        "snow": {
            "filter": "noise=alls=3:allf=t",
            "overlay": "overlay=x='if(gt(mod(t*30,100),50),W-w,0)':y='mod(t*20,H)'",
            "description": "雪花飘落效果"
        },
        "rain": {
            "filter": "noise=alls=2:allf=t",
            "overlay": "overlay=x='mod(t*50,W)':y='mod(t*100,H)'",
            "description": "雨滴下落效果"
        },
        "sparkle": {
            "filter": "noise=alls=2:allf=t",
            "overlay": "overlay=x='mod(sin(t*2)*W/2+W/2,W)':y='mod(cos(t*3)*H/2+H/2,H)'",
            "description": "闪烁光点效果"
        },
        "dust": {
            "filter": "noise=alls=2:allf=t",
            "overlay": "overlay=x='mod(t*10,W)':y='mod(t*5,H)'",
            "description": "灰尘飘散效果"
        }
    }

    if particle_type not in effects:
        particle_type = "snow"

    effect = effects[particle_type]

    # 根据强度调整透明度
    alpha_factor = int(255 * intensity)
    filter_str = effect["filter"].replace("255,0)", f"{alpha_factor},0)")
    overlay_str = effect["overlay"] + f":format=rgba:shortest=1"

    return {
        "filter": filter_str,
        "overlay": overlay_str,
        "description": effect["description"],
        "type": particle_type,
        "intensity": intensity
    }


def generate_light_effect(light_type="lens_flare", duration=5, intensity=0.4):
    """
    生成光效滤镜
    light_type: lens_flare(镜头光晕), beam(光束), glow(发光), flash(闪光)
    duration: 效果持续时间(秒)
    intensity: 效果强度 0.1-1.0
    """
    effects = {
        "lens_flare": {
            "filter": "boxblur=5:5",
            "blend": "blend=all_mode='screen':all_opacity=0.3",
            "description": "镜头光晕效果"
        },
        "beam": {
            "filter": "boxblur=3:3",
            "blend": "blend=all_mode='overlay':all_opacity=0.1",
            "description": "光束效果"
        },
        "glow": {
            "filter": "boxblur=10:10",
            "blend": "blend=all_mode='screen':all_opacity=0.4",
            "description": "发光效果"
        },
        "flash": {
            "filter": "eq=brightness=0.1:contrast=1.2",
            "blend": "blend=all_mode='addition':all_opacity=0.1",
            "description": "闪光效果"
        }
    }

    if light_type not in effects:
        light_type = "lens_flare"

    effect = effects[light_type]

    # 根据强度调整透明度
    opacity = intensity * 0.5  # 限制最大透明度
    blend_str = effect["blend"].replace(
        "0.3",
        f"{opacity:.2f}").replace(
        "0.2",
        f"{opacity:.2f}").replace(
            "0.4",
            f"{opacity:.2f}").replace(
                "0.1",
        f"{opacity:.2f}")

    return {
        "filter": effect["filter"],
        "blend": blend_str,
        "description": effect["description"],
        "type": light_type,
        "intensity": intensity
    }


def generate_transition_effect(
        transition_type="fade", duration=2, direction="in"):
    """
    生成转场效果滤镜
    transition_type: fade(淡入淡出), slide(滑动), zoom(缩放), wipe(擦除)
    duration: 转场持续时间(秒)
    direction: in(进入), out(退出)
    """
    effects = {
        "fade": {
            "filter": f"fade=t=in:st=0:d={duration}" if direction == "in" else f"fade=t=out:st=0:d={duration}",
            "description": "淡入淡出效果"
        },
        "slide": {
            "filter": "pad=iw*2:ih:iw:0,crop=iw/2:ih:'iw/2-iw/2*t/2':0" if direction == "in" else "pad=iw*2:ih:0:0,crop=iw/2:ih:'iw/2*t/2':0",
            "description": "滑动效果（使用pad+crop实现）"
        },
        "wipe": {
            "filter": "",  # 或可用基础滤镜如noise=alls=25:allf=t
            "description": "擦除效果（已禁用wiperight，防止报错）"
        }
    }

    if transition_type not in effects:
        transition_type = "fade"

    effect = effects[transition_type]

    return {
        "filter": effect["filter"],
        "description": effect["description"],
        "type": transition_type,
        "direction": direction,
        "duration": duration
    }


def apply_special_effects(video_path, output_path,
                         effects_config, log_func=None):
    """
    应用特效到视频
    effects_config: 特效配置字典
    """
    try:
        if log_func:
            log_func(f"[特效库] 开始应用特效: {effects_config}")

        # 构建特效滤镜链
        effect_filters = []
        has_blend = False
        blend_index = -1
        blend_str = ""
        # 应用粒子效果
        if effects_config.get("particle_effect"):
            particle = generate_particle_effect(
                effects_config["particle_effect"]["type"],
                effects_config["particle_effect"]["duration"],
                effects_config["particle_effect"]["intensity"]
            )
            if particle["filter"]:  # 检查滤镜是否为空
                effect_filters.append(particle["filter"])
                if log_func:
                    log_func(f"[特效库] 添加粒子效果: {particle['description']}")
        # 临时禁用光效以避免绿色异常问题
        if effects_config.get("light_effect"):
            if log_func:
                log_func(f"[特效库] 跳过光效以避免绿色异常: {effects_config['light_effect']['type']}")
            # 不添加光效滤镜
        # 应用转场效果
        if effects_config.get("transition_effect"):
            transition = generate_transition_effect(
                effects_config["transition_effect"]["type"],
                effects_config["transition_effect"]["duration"],
                effects_config["transition_effect"]["direction"]
            )
            if transition["filter"]:  # 检查滤镜是否为空
                effect_filters.append(transition["filter"])
                if log_func:
                    log_func(f"[特效库] 添加转场效果: {transition['description']}")
        # 构建完整滤镜链
        if effect_filters:
            # 过滤掉空字符串
            effect_filters = [f for f in effect_filters if f.strip()]
            if not effect_filters:
                if log_func:
                    log_func(f"[特效库] 所有滤镜均为空，跳过特效应用")
                shutil.copy2(video_path, output_path)
                return True

            # 强制禁用blend操作以避免绿色异常
            has_blend = False
            if log_func:
                log_func(f"[特效库] 强制禁用blend操作，使用简化滤镜链")

            if has_blend:
                # 只支持粒子+光效（带blend）+转场的简单场景
                # 获取视频的实际分辨率
                try:
                    probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
                                '-show_entries', 'stream=width,height', '-of', 'csv=s=x:p=0', video_path]
                    result_resolution = subprocess.run(probe_cmd, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
                    if result_resolution.returncode == 0:
                        resolution_str = result_resolution.stdout.strip()
                        width_str, height_str = resolution_str.split('x')
                        width, height = int(width_str), int(height_str)
                        if log_func:
                            log_func(f"[特效库] 检测到视频实际分辨率: {width}x{height}")
                    else:
                        width, height = 1920, 1080  # 默认分辨率
                        if log_func:
                            log_func(f"[特效库] 使用默认分辨率: {width}x{height}")
                except Exception as e:
                    width, height = 1920, 1080  # 默认分辨率
                    if log_func:
                        log_func(f"[特效库] 获取视频分辨率失败，使用默认值: {str(e)}")

                # 不再需要临时图像，直接对视频应用滤镜
                if log_func:
                    log_func(f"[特效库] 检测到视频分辨率: {width}x{height}, 直接应用滤镜")

                # 简化滤镜链构建，避免复杂的blend操作导致绿色异常
                if effect_filters:
                    # 直接应用基础滤镜到视频，不使用blend操作
                    base_filter = effect_filters[0] if effect_filters else "null"

                    # 构建简单的滤镜链，只对主视频流应用效果
                    filter_complex = f"[0:v]{base_filter}[outv]"

                    if log_func:
                        log_func(f"[特效库] 使用简化滤镜链，避免blend操作: {base_filter}")
                        log_func(f"[特效库] 跳过blend操作以避免绿色异常")
                # 检查硬件加速支持
                try:
                    encoders_cmd = ['ffmpeg', '-encoders']
                    encoders_check = subprocess.run(encoders_cmd,
                                                  stdout=subprocess.PIPE,
                                                  stderr=subprocess.PIPE,
                                                  timeout=5,
                                                  creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

                    use_effect_hardware = False
                    if encoders_check.returncode == 0:
                        encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                        if 'h264_nvenc' in encoders_output:
                            use_effect_hardware = True
                            if log_func:
                                log_func("[特效库] 启用CUDA硬件加速")
                        else:
                            if log_func:
                                log_func("[特效库] 使用CPU编码")
                    else:
                        if log_func:
                            log_func("[特效库] 使用CPU编码")
                except Exception as e:
                    use_effect_hardware = False
                    if log_func:
                        log_func(f"[特效库] 硬件加速检测异常，使用CPU编码")

                # 修改FFmpeg命令，不使用临时图像，直接应用滤镜到视频
                if use_effect_hardware:
                    cmd = [
                        'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', video_path,
                        '-filter_complex', filter_complex,
                        '-map', '[outv]',
                        '-map', '0:a?',  # 映射音频流（如果存在）
                        '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p',
                        '-b:v', '15000k', '-preset', 'p1',
                        '-c:a', 'copy',
                        output_path
                    ]
                else:
                    cmd = [
                        'ffmpeg', '-y', '-i', video_path,
                        '-filter_complex', filter_complex,
                        '-map', '[outv]',
                        '-map', '0:a?',  # 映射音频流（如果存在）
                        '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                        '-b:v', '15000k', '-preset', 'medium',
                        '-c:a', 'copy',
                        output_path
                    ]
                if log_func:
                    log_func(f"[特效库] 执行命令: {' '.join(cmd)}")
                proc_result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
                # 不再需要清理临时图像
            else:
                vf_chain = ",".join(effect_filters)

                # 检查硬件加速支持（非blend分支）
                try:
                    encoders_cmd = ['ffmpeg', '-encoders']
                    encoders_check = subprocess.run(encoders_cmd,
                                                  stdout=subprocess.PIPE,
                                                  stderr=subprocess.PIPE,
                                                  timeout=5)

                    use_effect_hardware = False
                    if encoders_check.returncode == 0:
                        encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                        if 'h264_nvenc' in encoders_output:
                            use_effect_hardware = True
                            if log_func:
                                log_func("[特效库] 启用CUDA硬件加速")
                        else:
                            if log_func:
                                log_func("[特效库] 使用CPU编码")
                    else:
                        if log_func:
                            log_func("[特效库] 使用CPU编码")
                except Exception as e:
                    use_effect_hardware = False
                    if log_func:
                        log_func(f"[特效库] 硬件加速检测异常，使用CPU编码")

                if use_effect_hardware:
                    cmd = [
                        'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', video_path,
                        '-vf', vf_chain,
                        '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p',
                        '-b:v', '15000k', '-preset', 'p1',
                        '-c:a', 'copy',
                        output_path
                    ]
                else:
                    cmd = [
                        'ffmpeg', '-y', '-i', video_path,
                        '-vf', vf_chain,
                        '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                        '-b:v', '15000k', '-preset', 'medium',
                        '-c:a', 'copy',
                        output_path
                    ]
                if log_func:
                    log_func(f"[特效库] 执行命令: {' '.join(cmd)}")
                proc_result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
            if proc_result.returncode == 0:
                if log_func:
                    log_func(f"[特效库] 特效应用成功: {output_path}")
                return True
            else:
                if log_func:
                    log_func(f"[特效库] 特效应用失败")
                    log_func(f"[特效库] 错误详情: {proc_result.stderr}")
                    log_func(f"[特效库] 命令: {' '.join(cmd)}")
                    if has_blend:
                        log_func(f"[特效库] filter_complex: {filter_complex}")
                    else:
                        log_func(f"[特效库] 滤镜链: {vf_chain}")
                return False
        else:
            # 没有特效，直接复制文件
            shutil.copy2(video_path, output_path)
            if log_func:
                log_func(f"[特效库] 无特效，直接复制文件")
            return True
    except Exception as e:
        if log_func:
            log_func(f"[特效库] 特效应用异常: {e}")
        return False


def cleanup_temp_files_on_failure(files_to_clean, success=False):
    """清理临时文件的统一函数"""
    try:
        if success:
            # 清理所有临时文件
            status_manager.log_detail("[伪原创] 处理成功，清理所有临时文件...")
            existing_files = [f for f in files_to_clean if f and os.path.exists(f)]
            cleaned_count = 0
            
            for f in files_to_clean:
                if f and os.path.exists(f):
                    try:
                        if os.path.isdir(f):
                            shutil.rmtree(f)
                            status_manager.log_detail(f"[伪原创] 已清理临时目录: {f}")
                        else:
                            os.remove(f)
                        cleaned_count += 1
                    except Exception as ex:
                        status_manager.log_detail(f"[伪原创] 临时文件清理失败: {f}, {ex}")
            
            status_manager.log_detail(f"[伪原创] 成功清理 {cleaned_count} 个临时文件/目录")
        else:
            status_manager.log_detail("[伪原创] 处理失败，保留临时文件用于调试")
            existing_files = [f for f in files_to_clean if f and os.path.exists(f)]
            status_manager.log_detail(f"[伪原创] 临时文件列表: {existing_files}")
    except Exception as cleanup_error:
        status_manager.log_detail(f"[伪原创] 清理过程中发生错误: {cleanup_error}")

@step_try_except(0, "伪原创主函数")
def pseudo_original_ffmpeg_two_step(input_path, output_path=None, progress_callback=None, log_callback=None,
                                   overlay_text=None, enable_rife=False, portrait_to_landscape=True, enable_watermark=True,
                                   vocal_sep_count=2, cover_mode='generate',  # 默认人声分离次数改为2
                                   cover_landscape=None, cover_portrait=None):
    # 添加函数入口点日志
    if log_callback:
        log_callback("======================================================")
        log_callback(f"[伪原创] 【重要】函数入口点，封面参数: cover_landscape={cover_landscape}, cover_portrait={cover_portrait}")
        log_callback(f"[伪原创] 【重要】函数入口点，人声分离次数参数: vocal_sep_count={vocal_sep_count}")
        log_callback("======================================================")
    # 导入需要的模块
    import random
    import time
    import colorsys
    import enhanced_logger as logger
    import os

    # 导入需要的模块
    import subprocess
    import shutil
    import traceback
    from status_manager import update_feature_status

    # 初始化全局临时文件列表和结果
    global_temp_files = []
    result = {"success": False, "error": "", "output_path": ""}

    # 定义内部函数 - 获取视频时长
    def get_duration(video_path):
        """获取视频时长
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            float: 视频时长(秒)，失败则返回0
        """
        try:
            # 使用status_manager的get_media_info方法获取视频信息
            info = status_manager.get_media_info(video_path)
            if info["is_valid"] and info["duration"] > 0:
                return info["duration"]
            else:
                if log_callback and info["error"]:
                    log_callback(f"[视频处理] 无法获取视频时长: {info['error']}")
                return 0
        except Exception as e:
            if log_callback:
                log_callback(f"[视频处理] 无法获取视频时长: {str(e)}")
            return 0
            
    # 初始化功能状态跟踪
    reset_feature_status()

    # 初始化状态管理器并设置日志函数
    status_manager.set_log_func(log_callback)

    # 初始化临时音频相关变量，避免在finally块中出现未定义错误
    temp_audio = None
    temp_audio_processed = None
    temp_audio_mixed = None
    
    # 初始化动态旋转相关变量，避免未定义错误
    rotate_filter_dyn = False
    rotate_amplitude = 0.0
    rotate_period = 10.0

    # 初始化返回值
    result = {
        "output": "",
        "success": True,
        "temp_files": []
    }
    
    # 辅助函数：添加临时文件到跟踪列表
    def add_temp_file(file_path):
        if file_path and file_path not in result["temp_files"]:
            result["temp_files"].append(file_path)

    if output_path is None:
        # 获取输入文件所在的目录和文件名
        input_dir = os.path.dirname(input_path)
        # 将输出文件命名为"视频.mp4"而不是原文件名加后缀
        output_path = os.path.join(input_dir, "视频.mp4")

    # 使用回调函数进行进度更新和日志输出
    log_func = log_callback
    parent_window = None
    dynamic_effects = []
    advanced_effects = []
    effects_config = {}
    decorations_config = {}
    # 将传入的横竖屏参数添加到step5_params - 默认值为True，明确表示需要进行横屏转竖屏处理
    step5_params = {'portrait_to_landscape': portrait_to_landscape}
    # 将传入的水印参数设为全局变量
    enable_watermark_global = enable_watermark
    # 确保overlay_text变量已正确初始化
    if overlay_text is None:
        overlay_text = ""
    
    # 初始化所有临时文件路径变量
    temp_step1 = None
    temp_step2 = None
    temp_step3 = None
    temp_step4 = None
    temp_step5 = None
    temp_step6 = None
    temp_step7 = None
    temp_step8 = None
    temp_step9 = None
    temp_step10 = None
    temp_audio_mixed = None
    temp_base = os.path.splitext(input_path)[0]
    temp_final = output_path

    try:
        # ========== 全局参数收集 ==========
        # Step 1
        step1_params = {}
        # Step 2
        step2_params = {}
        # Step 3
        step3_params = {}
        # Step 4
        step4_params = {}

        random_title = f"rand_{random.randint(100000, 999999)}"

        # 初始化全局临时文件列表，用于失败时的调试
        global_temp_files = []

        # Step 1: 轻量视频扰动（无水印、无动态滤镜、无插帧）
        status_manager.start_step("基础处理", 1, TOTAL_STEPS, input_path)
        status_manager.log_progress(1, 0)

        if log_func:
            log_func(f"[伪原创1] 使用输入文件: {input_path}")

        # 修改：使用新的文件命名约定
        temp_step1 = temp_base + ".step1.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step1)
        global_temp_files.append(temp_step1)
        
        # 验证输入文件是否存在和有效
        if not os.path.exists(input_path):
            error_details = f"输入文件不存在: {input_path}"
            if log_func:
                log_func(f"[伪原创1] 错误: {error_details}")
            status_manager.log_error(1, error_details, [])
            status_manager.complete_step("基础处理", 1, TOTAL_STEPS, success=False, details=error_details)
            result["success"] = False
            result["error"] = error_details
            cleanup_temp_files_on_failure(global_temp_files, success=False)
            return result
            
        # 验证输入文件是否为有效的媒体文件
        is_valid_input, input_info = status_manager.verify_file_detailed(
            1, input_path, expected_type="video", min_duration=0.1)
            
        if not is_valid_input:
            error_details = f"输入文件无效: {input_info['error']}"
            if log_func:
                log_func(f"[伪原创1] 错误: {error_details}")
                if 'ffprobe_output' in input_info:
                    log_func(f"[伪原创1] FFprobe输出: {input_info['ffprobe_output']}")
            status_manager.log_error(1, error_details, [])
            status_manager.complete_step("基础处理", 1, TOTAL_STEPS, success=False, details=error_details)
            result["success"] = False
            result["error"] = error_details
            return result
            
        if log_func:
            log_func(f"[伪原创1] 输入文件验证成功: {input_path}")
            log_func(f"[伪原创1] 文件信息: 时长={input_info['duration']:.2f}秒, 分辨率={input_info['resolution']}, 编码={input_info['video_codec']}")

        # 获取视频实际时长
        total_duration_step1 = get_duration(input_path)
        if total_duration_step1 > 0:
            status_manager.log_detail(
                f"[伪原创1] 检测到视频时长: {total_duration_step1:.2f}秒")
        else:
            total_duration_step1 = 200  # 默认值

        # 关闭裁剪功能，所有裁剪参数设为0
        left_crop = right_crop = top_crop = bottom_crop = 0
        crop_mode = 'no_crop'
        crop_filter = ''  # 不做裁剪
        pad_filter = ''   # 不做pad

        # 生成所有滤镜参数
        eq_brightness = random.uniform(0.005, 0.02)
        eq_contrast = random.uniform(1.01, 1.03)
        eq_filter = f"eq=brightness={eq_brightness:.3f}:contrast={eq_contrast:.2f}"
        mirror_flag = False
        mirror_filter = ""
        rotate_flag = False  # 关闭旋转功能
        rotate_deg = 0
        rotate_filter = ""
        sharpen_luma = round(random.uniform(0.8, 1.5), 2)
        sharpen_filter = f"unsharp=5:5:{sharpen_luma}:5:5:0.0"
        hue_val = round(random.uniform(-2, 2), 2)
        sat_val = round(random.uniform(0.95, 1.05), 2)
        color_filter = f"hue=h={hue_val}:s={sat_val}"
        blur_sigma = round(random.uniform(0.2, 0.6), 2)
        blur_filter = f"gblur=sigma={blur_sigma}" if random.random(
        ) < 0.7 else ""
        scale_ratio = round(random.uniform(1.01, 1.03), 3)
        x_shift = random.randint(-8, 8)
        y_shift = random.randint(0, 8)
        zoompan_filter = f"scale=iw*{scale_ratio}:ih*{scale_ratio},crop=iw:ih:{x_shift}:{y_shift}"
        # 移除帧率滤镜，避免音画不同步问题
        # framerate = round(random.uniform(29.7, 30.3), 2)
        # framerate_filter = f"fps={framerate}"
        framerate_filter = ""  # 禁用帧率滤镜
        border_thick = random.randint(2, 6)
        border_alpha = round(random.uniform(0.08, 0.18), 2)
        h_b = random.random()
        s_b = random.uniform(0.18, 0.32)
        l_b = random.uniform(0.78, 0.92)
        r_b, g_b, b_b = colorsys.hls_to_rgb(h_b, l_b, s_b)
        border_rgb = (int(r_b * 255) << 16) + \
            (int(g_b * 255) << 8) + int(b_b * 255)
        border_color = f"0x{border_rgb:06x}@{border_alpha}"
        border_filter = f"drawbox=0:0:iw:ih:{border_color}:t={border_thick}"
        noise_strength = random.randint(3, 8)
        noise_filter = f"noise=alls={noise_strength}:allf=t"

        # 其余滤镜链保持不变
        video_filters1 = [noise_filter, eq_filter, color_filter, blur_filter, mirror_filter, rotate_filter,
                          zoompan_filter, framerate_filter, border_filter, sharpen_filter, "scale=1920:1080,setsar=1"]
        video_filters1 = [f for f in video_filters1 if f]
        vf_str1 = ",".join(video_filters1)
        # 检查系统是否支持NVIDIA硬件加速
        # 使用与自动剪辑相同的策略：直接尝试GPU，失败时回退CPU
        use_hardware_accel = True

        # 简单检查：如果自动剪辑能用GPU，伪原创也应该能用
        # 采用正确的参数顺序进行测试
        try:
            # 根本原因：lavfi测试源(rgb24原始数据)不兼容CUDA硬件加速
            # 解决方案：改用简单的编码器可用性检查，避免复杂的流测试

            # 检查FFmpeg是否支持h264_nvenc编码器
            encoders_cmd = ['ffmpeg', '-encoders']
            encoders_check = subprocess.run(encoders_cmd,
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          timeout=5,
                                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

            if encoders_check.returncode == 0:
                encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                if 'h264_nvenc' in encoders_output:
                    if log_func:
                        log_func("[伪原创1] FFmpeg支持h264_nvenc编码器，启用CUDA硬件加速")
                    # 既然自动剪辑能成功使用GPU，我们也直接启用
                    use_hardware_accel = True
                else:
                    use_hardware_accel = False
                    if log_func:
                        log_func("[伪原创1] FFmpeg不支持h264_nvenc编码器，将使用CPU编码")
            else:
                use_hardware_accel = False
                if log_func:
                    log_func("[伪原创1] 无法检查FFmpeg编码器支持，将使用CPU编码")
        except Exception as e:
            use_hardware_accel = False
            if log_func:
                log_func(f"[伪原创1] 硬件加速检测异常: {str(e)[:100]}")
                log_func("[伪原创1] 将使用CPU编码")
                
        # 根据硬件加速能力选择不同的编码器参数
        # 添加音画同步参数确保处理过程中音视频保持同步
        if use_hardware_accel:
            cmd_step1 = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', input_path,
                '-vf', vf_str1,
                '-c:v', 'h264_nvenc',
                '-pix_fmt', 'yuv420p',
                '-b:v', '15000k',
                '-preset', 'p1',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_step1
            ]
        else:
            cmd_step1 = [
                'ffmpeg', '-y', '-i', input_path,
                '-vf', vf_str1,
                '-c:v', 'libx264',
                '-pix_fmt', 'yuv420p',
                '-b:v', '15000k',
                '-preset', 'medium',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_step1
            ]

        # 记录命令
        status_manager.log_command(1, cmd_step1)

        try:
            process1 = popen_subprocess(
                cmd_step1,
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace')
                
            if parent_window and hasattr(parent_window, 'active_processes'):
                parent_window.active_processes.append(process1)
        except Exception as e:
            error_details = f"启动ffmpeg进程失败: {str(e)}"
            if log_func:
                log_func(f"[伪原创1] 错误: {error_details}")
            status_manager.log_error(1, error_details, cmd_step1)
            status_manager.complete_step("基础处理", 1, TOTAL_STEPS, success=False, details=error_details)
            result["success"] = False
            result["error"] = error_details
            return result

        last_ffmpeg_log_time1 = 0
        stderr_lines1 = []
        last_progress_log1 = -10

        for line in process1.stderr:
            stderr_lines1.append(line)
            if "frame=" in line:
                now = time.time()
                if now - last_ffmpeg_log_time1 >= 30:
                    status_manager.log_detail(f"[伪原创1][ffmpeg] {line.strip()}")
                    last_ffmpeg_log_time1 = now

                    # 解析进度百分比
                    try:
                        if "time=" in line:
                            time_str = line.split("time=")[1].split()[0]
                            time_parts = time_str.split(":")
                            if len(time_parts) >= 3:
                                current_time = float(
                                    time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
                                # 使用预先获取的实际总时长
                                if total_duration_step1 > 0:
                                    percent = int(
                                        min(current_time / total_duration_step1, 1.0) * 100)
                                    if percent // 10 > last_progress_log1 // 10:
                                        status_manager.log_progress(
                                            1, percent // 10 * 10)
                                        last_progress_log1 = percent
                    except Exception as e:
                        if log_func:
                            log_func(f"[伪原创1] 解析进度时发生错误: {str(e)}")
                        pass
            else:
                status_manager.log_detail(f"[伪原创1][ffmpeg] {line.strip()}")

        process1.wait()
        # 确保进度到达100%
        status_manager.log_progress(1, 100)

        # 处理结果详情
        step1_detail = (
            "Step 1/11 轻量视频扰动参数：\n"
            f"裁剪模式={crop_mode}\n"
            f"裁剪=左{left_crop}px,右{right_crop}px,上{top_crop}px,下{0}px(保留字幕)\n"
            f"亮度扰动=brightness:{eq_brightness:.3f}\n"
            f"对比度扰动=contrast:{eq_contrast:.2f}\n"
            f"镜像={'是' if mirror_flag else '否'}\n"
            f"旋转={rotate_deg:+.2f}°\n"
            f"色相扰动=hue:{hue_val:+.2f}\n"
            f"饱和度扰动=saturation:{sat_val:+.2f}\n"
            f"模糊sigma={blur_sigma if blur_filter != '' else '无'}\n"
            f"缩放={scale_ratio}\n"
            f"平移=({x_shift},{y_shift})\n"
            f"帧率扰动={framerate if framerate_filter != '' else '无'}\n"
            f"边框厚度={border_thick if border_filter != '' else '无'}\n"
            f"锐化={sharpen_luma}\n"
            f"噪点=轻微(强度={noise_strength})\n"
            f"滤镜链={vf_str1}\n"
        )

        # 检查处理结果
        if process1.returncode == 0 and os.path.exists(temp_step1):
            # 详细验证文件有效性
            is_valid, file_info = status_manager.verify_file_detailed(
                1, temp_step1, expected_type="video", min_duration=1.0)

            if not is_valid:
                error_details = f"文件验证失败: {file_info['error']}"
                status_manager.log_error(1, error_details, cmd_step1)
                status_manager.complete_step(
                    "基础处理", 1, TOTAL_STEPS, success=False, details=error_details, output_file=temp_step1)
                result["success"] = False
                result["error"] = error_details
                return result

            # 第1步完成，文件信息将在第2步开始时显示，避免重复

            # 成功处理
            status_manager.log_detail(step1_detail)

            # 更详细的完成信息
            details = f"裁剪={crop_mode}, 亮度={eq_brightness:+.3f}, 对比度={eq_contrast:+.2f}, 色相={hue_val:+.2f}, 饱和度={sat_val:+.2f}, 锐化={sharpen_luma:.2f}"

            # 更新状态管理器
            status_manager.complete_step(
                "基础处理", 1, TOTAL_STEPS, success=True, details=details, output_file=temp_step1)

            # 简化的完成日志，避免冗余信息
            if log_func:
                log_func(f"[伪原创] Step 1/11 完成：{details}")
        else:
            # 处理失败 - 提供更详细的错误分析
            error_details = f"返回码: {process1.returncode}"
            
            # 解析ffmpeg错误输出，寻找常见错误模式
            error_reason = "未知错误"
            is_gpu_error = False

            if stderr_lines1:
                stderr_text = ''.join(stderr_lines1)

                # 检查是否是GPU相关错误
                gpu_error_patterns = [
                    "Error while opening encoder for output",
                    "Error initializing output stream",
                    "Cannot load nvcuda.dll",
                    "CUDA driver version is insufficient",
                    "No CUDA-capable device is detected",
                    "h264_nvenc",
                    "h264_cuvid",
                    "cuda"
                ]

                for pattern in gpu_error_patterns:
                    if pattern.lower() in stderr_text.lower():
                        is_gpu_error = True
                        break

                # 检查常见错误模式
                if "No such file or directory" in stderr_text:
                    error_reason = "文件或目录不存在"
                elif "Invalid data found when processing input" in stderr_text:
                    error_reason = "输入文件格式无效或损坏"
                elif "Error while opening encoder for output" in stderr_text:
                    error_reason = "编码器错误，可能是硬件加速不支持"
                elif "Error initializing output stream" in stderr_text:
                    error_reason = "输出流初始化失败"
                elif "Permission denied" in stderr_text:
                    error_reason = "权限被拒绝，无法访问文件或目录"
                elif "Out of memory" in stderr_text or "Cannot allocate memory" in stderr_text:
                    error_reason = "内存不足"
                elif "Unrecognized option" in stderr_text:
                    error_reason = "ffmpeg命令选项不被识别，可能版本不兼容"
                elif "No such filter" in stderr_text:
                    error_reason = "滤镜不存在，可能ffmpeg版本不支持"
                elif "Error opening filters" in stderr_text:
                    error_reason = "滤镜打开失败"
                elif "Conversion failed" in stderr_text:
                    error_reason = "转换失败"
                
                # 尝试提取更具体的错误信息
                for line in stderr_lines1:
                    if "Error" in line or "error" in line or "failed" in line or "Failed" in line:
                        error_reason = line.strip()
                        break
            
            # GPU错误回退机制
            if use_hardware_accel and is_gpu_error:
                if log_func:
                    log_func(f"[伪原创1] GPU处理失败: {error_reason}")
                    log_func("[伪原创1] 尝试回退到CPU编码...")

                # 构建CPU回退命令
                cmd_step1_cpu = [
                    'ffmpeg', '-y', '-i', input_path,
                    '-vf', vf_str1,
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-b:v', '15000k',
                    '-preset', 'medium',
                    '-c:a', 'copy',
                    temp_step1
                ]

                try:
                    if log_func:
                        log_func(f"[伪原创1] CPU回退命令: {' '.join(cmd_step1_cpu)}")

                    # 执行CPU回退
                    process1_cpu = subprocess.Popen(
                        cmd_step1_cpu,
                        stderr=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

                    # 简化的进度监控
                    for line in process1_cpu.stderr:
                        if "frame=" in line and "time=" in line:
                            try:
                                time_str = line.split("time=")[1].split()[0]
                                time_parts = time_str.split(":")
                                if len(time_parts) >= 3:
                                    current_time = float(time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
                                    if total_duration_step1 > 0:
                                        percent = int(min(current_time / total_duration_step1, 1.0) * 100)
                                        if percent % 20 == 0:  # 每20%显示一次
                                            status_manager.log_progress(1, percent)
                            except:
                                pass

                    process1_cpu.wait()
                    status_manager.log_progress(1, 100)

                    if process1_cpu.returncode == 0 and os.path.exists(temp_step1):
                        if log_func:
                            log_func("[伪原创1] CPU回退成功！")

                        # 验证CPU回退的输出文件
                        is_valid, file_info = status_manager.verify_file_detailed(
                            1, temp_step1, expected_type="video", min_duration=1.0)

                        if is_valid:
                            # CPU回退成功，完成第一步
                            details = f"GPU失败后CPU回退成功: 裁剪={crop_mode}, 亮度={eq_brightness:+.3f}, 对比度={eq_contrast:+.2f}"
                            status_manager.complete_step(
                                "基础处理", 1, TOTAL_STEPS, success=True, details=details, output_file=temp_step1)

                            # 记录详细的处理结果
                            status_manager.log_detail(f"[伪原创] Step 1/11 完成（CPU回退）：裁剪=左{left_crop}px,右{right_crop}px,上{top_crop}px,下{0}px(保留字幕)，亮度扰动={eq_brightness:+.3f}，对比度扰动={eq_contrast:+.2f}，镜像={'是' if mirror_flag else '否'}，旋转={rotate_deg:+.2f}°，色相扰动={hue_val:+.2f}，饱和度扰动={sat_val:+.2f}，模糊sigma={blur_sigma if blur_filter != '' else '无'}，缩放={scale_ratio}，平移=({x_shift},{y_shift})，帧率扰动={framerate if framerate_filter != '' else '无'}，边框厚度={border_thick if border_filter != '' else '无'}，锐化={sharpen_luma}，噪点=轻微(强度={noise_strength})")

                            # CPU回退成功，继续到第二步（不return，让代码自然流向第二步）
                        else:
                            raise Exception("CPU回退文件验证失败")
                    else:
                        raise Exception(f"CPU回退也失败，返回码: {process1_cpu.returncode}")

                except Exception as cpu_error:
                    if log_func:
                        log_func(f"[伪原创1] CPU回退也失败: {str(cpu_error)}")

                    # CPU回退也失败，记录完整错误并返回失败
                    final_error = f"GPU失败: {error_reason}, CPU回退也失败: {str(cpu_error)}"
                    status_manager.log_error(1, final_error, cmd_step1)
                    status_manager.complete_step("基础处理", 1, TOTAL_STEPS, success=False, details=final_error)
                    result["success"] = False
                    result["error"] = final_error
                    return result
            else:
                # 非GPU错误或CPU模式失败，直接返回失败
                error_details = f"返回码: {process1.returncode}, 原因: {error_reason}"

                if log_func:
                    log_func(f"[伪原创1] 处理失败: {error_details}")
                    if stderr_lines1:
                        log_func("[伪原创1] 错误输出摘要:")
                        for i in range(min(5, len(stderr_lines1))):
                            if "Error" in stderr_lines1[i] or "error" in stderr_lines1[i]:
                                log_func(f"[伪原创1] {stderr_lines1[i].strip()}")

                status_manager.log_error(1, error_details, cmd_step1)
                status_manager.complete_step("基础处理", 1, TOTAL_STEPS, success=False, details=error_details)
                result["success"] = False
                result["error"] = error_details
                return result

        # 第1步已完成，移除对未定义变量的引用

        # Step 2: 只加滤镜（无水印）
        if log_func:
            log_func(f"[伪原创2] 使用输入文件: {temp_step1}")
        temp_step2 = temp_base + ".step2.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step2)
        global_temp_files.append(temp_step2)

        # 先验证输入文件 - 修正顺序：在步骤开始前先验证
        is_valid, input_info = status_manager.verify_file_detailed(
            2, temp_step1, expected_type="video", log_results=False)
        if not is_valid:
            error_details = f"输入文件验证失败: {input_info['error']}，无法继续处理"
            status_manager.log_error(2, error_details)
            result["success"] = False
            result["error"] = error_details
            return result
            
        # 正式开始第二步
        status_manager.start_step("滤镜处理", 2, TOTAL_STEPS, temp_step1)

        # 验证步骤转换
        if not verify_step_transition(temp_step1, temp_step2, 1, 2, "滤镜处理", result):
            return result

        # 确认输入文件包含视频流
        streams = get_stream_types(temp_step1)
        has_video = 'video' in streams
        
        if not has_video:
            if log_func:
                log_func(f"[伪原创] 警告: 输入文件 {temp_step1} 不包含视频流！将只处理音频")
                log_func(f"[伪原创] 检测到流: {', '.join(streams)}")
        
        # 设置输入文件类型标志
        input_has_video = has_video
        
        if log_func:
            if input_has_video:
                log_func(f"[伪原创] 输入文件包含视频流，将应用视频滤镜")
            else:
                log_func(f"[伪原创] 输入文件不包含视频流，将只处理音频")

        # 动态滤镜链 - 添加实时变化的特效
        # 只有视频流才应用视频滤镜
        dynamic_effects = []
        hue_amplitude = hue_period = brightness_amplitude = brightness_period = rotate_amplitude = rotate_period = 0

        # 初始化视频滤镜标志变量
        hue_filter_dyn = False
        fade_filter_dyn = False
        insert_frame_flag = False
        drop_frame_flag = False

        # 只有当输入文件包含视频流时才应用视频滤镜
        if input_has_video:
            # 1. 动态色相变化（颜色随时间缓慢变化）
            if random.random() < 0.7:  # 70%概率添加
                hue_amplitude = random.uniform(0.05, 0.2)  # 色相变化幅度
                hue_period = random.uniform(8, 15)  # 变化周期8-15秒
                # 使用表达式让色相随时间变化，修复表达式语法
                hue_expr = f"{hue_amplitude}*sin(2*PI*t/{hue_period})"
                # 使用FFmpeg内置的pi常量代替PI，使用单引号包围整个表达式
                ffmpeg_hue_expr = f"'{hue_amplitude}*sin(2*PI*t/{hue_period})'"
                dynamic_effects.append(f"hue=h={ffmpeg_hue_expr}")
                hue_filter_dyn = True
                if log_func:
                    log_func(f"[伪原创2] 添加动态色相变化: 幅度={hue_amplitude:.3f}, 周期={hue_period:.1f}秒")

        # 2. 动态亮度/对比度（画面明暗度轻微波动）- 只有视频流才应用
        if input_has_video and random.random() < 0.7:  # 70%概率添加
            brightness_amplitude = random.uniform(0.01, 0.03)  # 亮度变化幅度
            brightness_period = random.uniform(10, 20)  # 变化周期10-20秒
            # 使用表达式让亮度随时间变化，修复表达式语法
            brightness_expr = f"{brightness_amplitude}*sin(2*PI*t/{brightness_period})"
            # 使用FFmpeg内置的pi常量代替PI，使用单引号包围整个表达式
            ffmpeg_brightness_expr = f"'{brightness_amplitude}*sin(2*PI*t/{brightness_period})'"
            dynamic_effects.append(f"eq=brightness={ffmpeg_brightness_expr}")
            fade_filter_dyn = True
            if log_func:
                log_func(f"[伪原创2] 添加动态亮度变化: 幅度={brightness_amplitude:.3f}, 周期={brightness_period:.1f}秒")

        # 4. 动态缩放（画面轻微放大缩小）- 只有视频流才应用
        # 注意：移除zoompan滤镜，因为d=1参数会导致视频时长大幅增加
        if input_has_video and random.random() < 0.3:  # 30%概率添加
            # 使用安全的缩放效果，不会改变时长
            scale_factor = random.uniform(1.001, 1.01)  # 轻微缩放
            dynamic_effects.append(f"scale=iw*{scale_factor:.6f}:ih*{scale_factor:.6f}")
            if log_func:
                log_func(f"[伪原创2] 添加安全缩放: 倍数={scale_factor:.4f}")


        # 5. 动态模糊（清晰度轻微变化）- 只有视频流才应用
        # 注意：FFmpeg的gblur滤镜不支持表达式参数，因此我们使用多个不同模糊程度的滤镜并使用过渡
        if input_has_video and random.random() < 0.25:  # 25%概率添加
            # 由于gblur不支持动态参数，我们使用智能模糊(smartblur)滤镜代替
            # 使用固定值代替动态表达式，因为smartblur不支持动态参数
            blur_amplitude = random.uniform(0.3, 0.8)  # 模糊强度
            # 使用固定值
            fixed_blur_value = blur_amplitude
            dynamic_effects.append(f"smartblur=lr={fixed_blur_value:.2f}:ls=1.0")
            if log_func:
                log_func(f"[伪原创2] 添加固定模糊: 强度={fixed_blur_value:.3f}")

        # 6. 智能插帧功能（提高视频流畅度，保持时长不变）- 只有视频流才应用
        # 修改：启用插帧功能，但使用更保守的插帧数量（30-60帧）
        insert_frame_flag = True  # 启用插帧功能，使用保守的插帧数量

        # 如果有视频流，处理插帧
        if insert_frame_flag and input_has_video:
            import config_manager
            config = config_manager.get_config()

            # 获取输入视频的帧率
            input_fps = get_video_fps(temp_step1)
            if input_fps <= 0:
                input_fps = 30
                if log_func:
                    log_func(f"[伪原创] 无法获取视频帧率，使用默认值：{input_fps}fps")
            else:
                if log_func:
                    log_func(f"[伪原创2] 检测到视频帧率：{input_fps}fps")

            # 获取视频总时长
            try:
                video_duration = get_duration(temp_step1)
                if video_duration <= 0:
                    video_duration = 240  # 默认4分钟
            except:
                video_duration = 240

            # 计算原始总帧数
            original_frame_count = int(input_fps * video_duration)

            # 修改插帧逻辑：使用固定的插帧数量（5-10帧）
            # 随机生成插帧数量（5-10帧）
            insert_frames_count = random.randint(5, 10)

            # 计算目标帧率（保持时长不变）
            new_frame_count = original_frame_count + insert_frames_count
            target_fps = new_frame_count / video_duration

            # 将插帧数量存储到全局变量，供第4步使用
            global step2_insert_frames
            step2_insert_frames = insert_frames_count

            if log_func:
                log_func(f"[伪原创2] 固定插帧模式:")
                log_func(f"[伪原创2]   原始帧数: {original_frame_count}帧")
                log_func(f"[伪原创2]   插入帧数: {insert_frames_count}帧")
                log_func(f"[伪原创2]   新总帧数: {new_frame_count}帧")
                log_func(f"[伪原创2]   原始帧率: {input_fps:.2f}fps")
                log_func(f"[伪原创2]   目标帧率: {target_fps:.2f}fps")
                log_func(f"[伪原创2]   时长保持: {video_duration:.2f}秒 (无变化)")

            # 根据配置选择插帧模式
            interpolation_mode = config.get('complex_interpolation', 'fast')
            mi_mode = 'dup'  # 默认使用dup模式（快速模式）

            if isinstance(interpolation_mode, bool):
                # 兼容旧版配置（布尔值）
                mi_mode = 'mci' if interpolation_mode else 'blend'
            elif interpolation_mode == 'normal':
                mi_mode = 'blend'
            elif interpolation_mode == 'slow':
                mi_mode = 'mci'

            # 根据不同模式设置不同参数
            if mi_mode == 'mci':
                # 使用mci模式（运动补偿插值）- 最高质量，最慢
                minterpolate_filter = f"minterpolate=fps={target_fps:.2f}:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1"
            elif mi_mode == 'blend':
                # 使用blend模式（混合插值）- 中等质量，中等速度
                minterpolate_filter = f"minterpolate=fps={target_fps:.2f}:mi_mode=blend"
            else:
                # 使用dup模式（帧复制）- 最低质量，最快
                minterpolate_filter = f"minterpolate=fps={target_fps:.2f}:mi_mode=dup"

            dynamic_effects.append(minterpolate_filter)

            if log_func:
                log_func(f"[伪原创2] 使用minterpolate滤镜(固定插帧模式)，目标帧率={target_fps:.2f}fps")
        else:
            # 如果跳过插帧，添加替代的轻量级效果
            if log_func:
                log_func(f"[伪原创2] 跳过插帧处理，添加替代效果")

            # 添加不改变时长的轻量级效果
            if random.random() < 0.7:
                # 添加轻微的亮度波动
                brightness_amplitude = random.uniform(0.005, 0.015)
                brightness_period = random.uniform(10, 20)
                brightness_effect = f"eq=brightness='{brightness_amplitude:.6f}*sin(2*PI*t/{brightness_period:.6f})'"
                dynamic_effects.append(brightness_effect)
                if log_func:
                    log_func(f"[伪原创2] 添加亮度波动: 幅度={brightness_amplitude:.3f}, 周期={brightness_period:.1f}秒")

            if random.random() < 0.5:
                # 添加轻微的色相调整
                hue_shift = random.uniform(-0.1, 0.1)
                dynamic_effects.append(f"hue=h={hue_shift:.3f}")
                if log_func:
                    log_func(f"[伪原创2] 添加色相调整: {hue_shift:.3f}")

        # 移除抽帧功能，将其转移到步骤4(视频增强)中
        
        # 注意：水印功能将在Step 3中处理
        if log_func:
            log_func(f"[伪原创2] 注意：水印功能将在Step 3中处理")

        # 获取总视频时长，用于计算进度
        try:
            total_duration = get_duration(temp_step1)
            if log_func:
                log_func(f"[伪原创2] 检测到总视频时长: {total_duration:.2f}秒")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创2] 警告：无法获取总视频时长，使用默认值 - {str(e)}")
            total_duration = 200  # 默认值

        # 构建步骤2的滤镜链 - 调整顺序，minterpolate放在最前面
        if dynamic_effects:
            # 分离minterpolate滤镜和其他滤镜
            minterpolate_filters = [f for f in dynamic_effects if f.startswith("minterpolate")]
            other_filters = [f for f in dynamic_effects if not f.startswith("minterpolate")]

            # 重新排序：minterpolate在最前面，其他滤镜在后面
            ordered_filters = minterpolate_filters + other_filters
            vf_str2 = ",".join(ordered_filters)

            # 确保滤镜链中包含scale和setsar滤镜，以保持视频尺寸一致
            if "scale=" not in vf_str2:
                vf_str2 += ",scale=1920:1080,setsar=1"
            
            # 添加安全措施，确保至少保留第一帧
            if "select=" in vf_str2 and "eq(n,0)" not in vf_str2:
                vf_str2 = vf_str2.replace("select='", "select='eq(n,0)+")
                
            if log_func:
                log_func(f"[伪原创2] 步骤2滤镜链: {vf_str2}")
        else:
            # 如果没有动态效果，使用简单的copy滤镜
            vf_str2 = "null"
            if log_func:
                log_func("[伪原创2] 无动态效果，使用null滤镜")
                
        # 执行步骤2的ffmpeg命令
        # 第2步应该与第1步使用相同的硬件加速设置
        # 添加音画同步参数，特别是当使用minterpolate滤镜时
        if use_hardware_accel:
            cmd_step2 = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_step1,
                '-vf', vf_str2,
                '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-b:v', '15000k', '-preset', 'p1',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_step2
            ]
        else:
            cmd_step2 = [
                'ffmpeg', '-y', '-i', temp_step1,
                '-vf', vf_str2,
                '-c:v', 'libx264',
                '-pix_fmt', 'yuv420p',
                '-b:v', '15000k',
                '-preset', 'medium',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_step2
            ]
        
        if log_func:
            log_func(f"[伪原创2] 执行命令: {' '.join(cmd_step2)}")
            
        process2 = subprocess.Popen(
            cmd_step2,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace',
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
            
        if parent_window and hasattr(parent_window, 'active_processes'):
            parent_window.active_processes.append(process2)
            
        last_ffmpeg_log_time2 = 0
        stderr_lines2 = []
        last_progress_log2 = -10
        
        for line in process2.stderr:
            stderr_lines2.append(line)
            if "frame=" in line:
                now = time.time()
                if now - last_ffmpeg_log_time2 >= 30:
                    status_manager.log_detail(f"[伪原创2][ffmpeg] {line.strip()}")
                    last_ffmpeg_log_time2 = now
                    
                    # 解析进度百分比
                    try:
                        if "time=" in line:
                            time_str = line.split("time=")[1].split()[0]
                            time_parts = time_str.split(":")
                            if len(time_parts) >= 3:
                                current_time = float(time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
                                # 使用预先获取的实际总时长
                                if total_duration > 0:
                                    percent = int(min(current_time / total_duration, 1.0) * 100)
                                    if percent // 10 > last_progress_log2 // 10:
                                        status_manager.log_progress(2, percent // 10 * 10)
                                        last_progress_log2 = percent
                    except Exception:
                        pass
            else:
                status_manager.log_detail(f"[伪原创2][ffmpeg] {line.strip()}")
                
        process2.wait()
        status_manager.log_progress(2, 100)
        
        # 检查处理结果
        if process2.returncode != 0 or not os.path.exists(temp_step2):
            # 初始化错误详情
            error_details = f"步骤2处理失败: 返回码={process2.returncode}"

            # 解析ffmpeg错误输出，寻找常见错误模式
            error_reason = "未知错误"
            is_gpu_error = False

            if stderr_lines2:
                stderr_text = ''.join(stderr_lines2)

                # 检查是否是GPU相关错误
                gpu_error_patterns = [
                    "Error while opening encoder for output",
                    "Error initializing output stream",
                    "Cannot load nvcuda.dll",
                    "CUDA driver version is insufficient",
                    "No CUDA-capable device is detected",
                    "h264_nvenc",
                    "h264_cuvid",
                    "cuda"
                ]

                for pattern in gpu_error_patterns:
                    if pattern.lower() in stderr_text.lower():
                        is_gpu_error = True
                        break

                # 检查常见错误模式
                if "No such file or directory" in stderr_text:
                    error_reason = "文件或目录不存在"
                elif "Invalid data found when processing input" in stderr_text:
                    error_reason = "输入文件格式无效或损坏"
                elif "Error while opening encoder for output" in stderr_text:
                    error_reason = "编码器错误，可能是硬件加速不支持"
                elif "Error initializing output stream" in stderr_text:
                    error_reason = "输出流初始化失败"
                elif "Permission denied" in stderr_text:
                    error_reason = "权限被拒绝，无法访问文件或目录"
                elif "Out of memory" in stderr_text or "Cannot allocate memory" in stderr_text:
                    error_reason = "内存不足"
                elif "Unrecognized option" in stderr_text:
                    error_reason = "ffmpeg命令选项不被识别，可能版本不兼容"
                elif "No such filter" in stderr_text:
                    error_reason = "滤镜不存在，可能ffmpeg版本不支持"
                elif "Error opening filters" in stderr_text:
                    error_reason = "滤镜打开失败"
                elif "Conversion failed" in stderr_text:
                    error_reason = "转换失败"

                # 尝试提取更具体的错误信息
                for line in stderr_lines2:
                    if "Error" in line or "error" in line or "failed" in line or "Failed" in line:
                        error_reason = line.strip()
                        break

            # GPU错误回退机制（只有在使用GPU时才回退）
            if use_hardware_accel and is_gpu_error:
                if log_func:
                    log_func(f"[伪原创2] GPU处理失败: {error_reason}")
                    log_func("[伪原创2] 尝试回退到CPU编码...")

                # 构建CPU回退命令
                cmd_step2_cpu = [
                    'ffmpeg', '-y', '-i', temp_step1,
                    '-vf', vf_str2,
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-b:v', '15000k',
                    '-preset', 'medium',
                    '-c:a', 'copy',
                    temp_step2
                ]

                try:
                    if log_func:
                        log_func(f"[伪原创2] CPU回退命令: {' '.join(cmd_step2_cpu)}")

                    # 执行CPU回退
                    process2_cpu = subprocess.Popen(
                        cmd_step2_cpu,
                        stderr=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

                    # 简化的进度监控
                    for line in process2_cpu.stderr:
                        if "frame=" in line and "time=" in line:
                            try:
                                time_str = line.split("time=")[1].split()[0]
                                time_parts = time_str.split(":")
                                if len(time_parts) >= 3:
                                    current_time = float(time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
                                    if total_duration > 0:
                                        percent = int(min(current_time / total_duration, 1.0) * 100)
                                        if percent % 20 == 0:  # 每20%显示一次
                                            status_manager.log_progress(2, percent)
                            except:
                                pass

                    process2_cpu.wait()
                    status_manager.log_progress(2, 100)

                    if process2_cpu.returncode == 0 and os.path.exists(temp_step2):
                        if log_func:
                            log_func("[伪原创2] CPU回退成功！")

                        # 验证CPU回退的输出文件
                        is_valid, file_info = status_manager.verify_file_detailed(
                            2, temp_step2, expected_type="video", min_duration=1.0)

                        if is_valid:
                            # CPU回退成功，继续正常流程
                            if log_func:
                                log_func("[伪原创2] CPU回退文件验证成功，继续处理...")
                            # 跳出错误处理，继续第3步
                        else:
                            raise Exception("CPU回退文件验证失败")
                    else:
                        raise Exception(f"CPU回退也失败，返回码: {process2_cpu.returncode}")

                except Exception as cpu_error:
                    if log_func:
                        log_func(f"[伪原创2] CPU回退也失败: {str(cpu_error)}")

                    # CPU回退也失败，记录完整错误并返回失败
                    final_error = f"GPU失败: {error_reason}, CPU回退也失败: {str(cpu_error)}"
                    status_manager.log_error(2, final_error, cmd_step2)
                    update_feature_status("伪原创2-滤镜处理", "失败", final_error)
                    result["success"] = False
                    result["error"] = final_error
                    return result
            else:
                # 非GPU错误，直接返回失败
                error_details = f"步骤2处理失败: 返回码={process2.returncode}, 原因: {error_reason}"

                if log_func:
                    log_func(f"[伪原创2] 处理失败: {error_details}")
                    if stderr_lines2:
                        log_func("[伪原创2] 错误输出摘要:")
                        for i in range(min(5, len(stderr_lines2))):
                            if "Error" in stderr_lines2[i] or "error" in stderr_lines2[i]:
                                log_func(f"[伪原创2] {stderr_lines2[i].strip()}")

                status_manager.log_error(2, error_details, cmd_step2)
                status_manager.log_detail("[伪原创] Step 2/11 ffmpeg错误输出（最后20行）：\n" + ''.join(stderr_lines2[-20:]))
                update_feature_status("伪原创2-滤镜处理", "失败", error_details)
                result["success"] = False
                result["error"] = error_details
                return result
            
        # 步骤2完成后，进行步骤3的准备工作
        # 水印处理会在步骤3中进行，这里不需要处理水印相关逻辑
        
        # 验证步骤2的输出是否包含视频流
        streams_check = get_stream_types(temp_step2)
        has_video_check = 'video' in streams_check
        
        if not has_video_check:
            if log_func:
                log_func(f"[伪原创2] 错误: 步骤2输出文件不包含视频流，处理中止")
                log_func(f"[伪原创2] 检测到流: {', '.join(streams_check)}")
                log_func("[伪原创2] 请检查滤镜设置，确保不会过滤掉所有视频帧")

            # 更新功能状态 - 修正：更新步骤2的状态，而不是步骤3
            update_feature_status("伪原创2-滤镜处理", "失败", "输出文件不包含视频流，处理中止")
            
            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = "步骤2输出文件不包含视频流，处理中止"
            return result
        # 步骤2完成后，进入步骤3：水印处理
        # 正式开始第三步，使用状态管理器
        status_manager.start_step("水印处理", 3, TOTAL_STEPS, temp_step2)
        status_manager.log_progress(3, 0)

        if log_func:
            log_func("[伪原创3] Step 3/11: 水印处理开始...")
            log_func(f"[伪原创3] 使用输入文件: {temp_step2}")
        
        # 确保temp_step3已初始化
        temp_step3 = temp_base + ".step3.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step3)
        global_temp_files.append(temp_step3)
        
        # 使用verify_step_transition函数验证步骤之间的文件转换
        if not verify_step_transition(temp_step2, temp_step3, 2, 3, "水印处理", result):
            # 验证失败，返回结果
            return result
            
        # 判断是否需要应用水印
        skip_watermark = False
        try:
            import config_manager
            config = config_manager.get_config()
            # 优先使用传入的enable_watermark_global参数，其次从配置中读取
            skip_watermark = not enable_watermark_global and not config.get('enable_watermark', True)
        except Exception as e:
            if log_func:
                log_func(f"[伪原创3] 读取配置时出错: {str(e)}")
            skip_watermark = True  # 默认不应用水印

        if skip_watermark:
            if log_func:
                log_func("[伪原创3] 水印功能已禁用，跳过水印处理")
            # 直接复制文件，跳过水印处理
            shutil.copy2(temp_step2, temp_step3)
            status_manager.log_detail("[伪原创3] 水印功能已禁用，跳过水印处理")
            status_manager.complete_step("水印处理", 3, TOTAL_STEPS, success=True, details="水印功能已禁用")
        else:
            if log_func:
                log_func("[伪原创3] 水印功能已启用，开始水印处理")
            # 生成水印
            try:
                import config_manager
                config = config_manager.get_config()
                watermark_text = config.get('watermark_text', '带你看历史')
                fontcolor = config.get('fontcolor', 'white')
                fontsize = config.get('fontsize', 36)
                position = config.get('position', 'bottom-right')
                opacity = config.get('opacity', 0.8)
                font_path = config.get('font_path', 'SourceHanSansSC-Medium.otf')
                font_path = os.path.join(os.path.dirname(__file__), 'fonts', font_path)
                if not os.path.exists(font_path):
                    # 如果项目字体不存在，尝试系统字体
                    font_path = "C:/Windows/Fonts/simhei.ttf"  # 使用系统黑体
                if log_func:
                    log_func(f"[伪原创3] 使用字体文件: {font_path}")
            except Exception as e:
                if log_func:
                    log_func(f"[伪原创3] 读取配置时出错: {str(e)}")
                # 使用默认值
                watermark_text = '带你看历史'
                fontcolor = 'white'
                fontsize = 36
                position = 'bottom-right'
                opacity = 0.8
                font_path = "C:/Windows/Fonts/simhei.ttf"  # 使用系统黑体
                if log_func:
                    log_func("[伪原创3] 使用默认字体: 系统黑体")

            # 初始化水印控制变量
            enable_watermark_local = enable_watermark_global
            skip_watermark = False
            
            # 生成水印
            try:
                import config_manager
                config = config_manager.get_config()
                watermark_text = config.get('watermark_text', '带你看历史')
                fontcolor = config.get('fontcolor', 'white')
                fontsize = config.get('fontsize', 36)
                position = config.get('position', 'bottom-right')
                opacity = config.get('opacity', 0.8)
                # 固定使用微软雅黑字体，确保所有位置字体一致
                font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
                font_name = "微软雅黑"

                # 如果微软雅黑不存在，使用备选字体
                if not os.path.exists(font_path):
                    font_path = "C:/Windows/Fonts/simhei.ttf"  # 黑体备选
                    font_name = "黑体"

                # 如果系统字体都不存在，使用项目字体
                if not os.path.exists(font_path):
                    project_font = config.get('font_path', 'SourceHanSansSC-Medium.otf')
                    font_path = os.path.join(os.path.dirname(__file__), 'fonts', project_font)
                    font_name = "项目字体"
                if log_func:
                    log_func(f"[伪原创3] 固定字体选择: {font_name}")
                    log_func(f"[伪原创3] 字体文件路径: {font_path}")
                    log_func(f"[伪原创3] 字体一致性保证: 所有位置统一使用{font_name}")
            except Exception as e:
                if log_func:
                    log_func(f"[伪原创3] 读取字体配置失败: {str(e)}")
                skip_watermark = not enable_watermark_local or (config and config.get('skip_watermark', False))
                
                if log_func:
                    if not enable_watermark_local:
                        log_func("[伪原创3] 水印功能已关闭（按用户设置）")
                    elif skip_watermark:
                        log_func("[伪原创3] 已跳过水印处理（按配置跳过）")
                    elif not overlay_text or not overlay_text.strip():
                        log_func("[伪原创3] 已跳过水印处理（无文字内容）")
        
        # 文字水印处理逻辑 - 只支持文字水印
        has_text_watermark = overlay_text and overlay_text.strip() and enable_watermark_local

        # 文字水印处理 - 使用两步法避免音画不同步
        if skip_watermark or not has_text_watermark:
            # 如果跳过水印或没有文字内容，直接复制文件
            import shutil
            shutil.copy(temp_step2, temp_step3)
            
            if log_func:
                if not enable_watermark_local:
                    log_func("[伪原创3] 已跳过水印处理（按用户设置关闭水印功能）")
                elif skip_watermark:
                    log_func("[伪原创3] 已跳过水印处理（按配置跳过）")
                elif not has_text_watermark:
                    log_func("[伪原创3] 已跳过水印处理（无文字内容）")
        else:
            # 两步法水印处理
            import config_manager
            config = config_manager.get_config()
            watermark_text = config.get('watermark_text', '带你看历史')

            if log_func:
                log_func(f"[伪原创3] 正在应用文字水印（一步法）: {watermark_text}")

            # 水印参数设置
            font_size = 36

            # 随机选择位置（左上角和右上角）
            positions = [
                {"x": "20", "y": "20"},                    # 左上角
                {"x": "w-text_w-20", "y": "20"}           # 右上角
            ]
            chosen = random.choice(positions)

            # 随机温和颜色
            gentle_colors = [
                "#AEEEEE", "#B4EEB4", "#EED2EE", "#FFE4B5", "#FFFACD",
                "#E0EEE0", "#FFDAB9", "#E0FFFF", "#F0E68C", "#F5DEB3"
            ]
            fontcolor = random.choice(gentle_colors)

            # 使用配置的中文水印文本
            chinese_text = watermark_text  # 使用配置的水印文字

            # 使用ensure_font_available函数获取安全的字体路径
            font_file_path = ensure_font_available('SourceHanSansSC-Medium.otf', log_func)
            font_display_name = "思源黑体"
            
            # 如果返回的是系统字体，更新显示名称
            if "simhei.ttf" in font_file_path:
                font_display_name = "黑体"
            elif "msyh.ttc" in font_file_path:
                font_display_name = "微软雅黑"
            elif "arial.ttf" in font_file_path:
                font_display_name = "Arial"

            # 转换路径格式，确保FFmpeg能正确识别
            # 注意：drawtext滤镜中不能使用单引号包围路径，需要转义空格
            font_file_path_escaped = font_file_path.replace('\\', '/').replace(' ', '\\ ')

            # 转义文本中的特殊字符
            escaped_text = chinese_text.replace("'", "\\'").replace(":", "\\:").replace("\\", "\\\\")
            
            # 完全使用字体名称而不是路径，避免路径解析问题
            # 使用简单的drawtext命令，只使用字体名称
            drawtext_filter = f"drawtext=font=SimHei:text='{escaped_text}':fontsize={font_size}:fontcolor={fontcolor}:x={chosen['x']}:y={chosen['y']}"
            
            if log_func:
                log_func(f"[伪原创3] 使用字体名称: SimHei (黑体)")
                
            # 如果需要调试
            if log_func:
                log_func(f"[伪原创3] 完整的drawtext滤镜: {drawtext_filter}")
            
            # 备用方案：如果需要使用fontfile，确保路径没有空格
            # drawtext_filter = f"drawtext=fontfile={font_file_path_escaped}:text='{escaped_text}':fontsize={font_size}:fontcolor={fontcolor}:x={chosen['x']}:y={chosen['y']}"

            if log_func:
                log_func(f"[伪原创3] 选择字体: {font_display_name}")
                log_func(f"[伪原创3] 字体路径: {font_file_path}")
                log_func(f"[伪原创3] 水印位置: {chosen}, 颜色: {fontcolor}")

            # 一步法：直接在原视频上添加水印，保持音画同步和文件大小
            # 优先使用硬件加速，失败时自动回退到软件编码
            if use_hardware_accel:
                if log_func:
                    log_func("[伪原创3] 使用CUDA硬件加速处理水印")

                cmd_watermark = [
                    'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y',
                    '-i', temp_step2,
                    '-vf', drawtext_filter,
                    '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-b:v', '15000k', '-preset', 'p1',
                    '-c:a', 'copy',                     # 音频直接复制，不重编码
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-copyts',                          # 复制原始时间戳，保持同步
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    temp_step3
                ]
            else:
                if log_func:
                    log_func("[伪原创3] 使用软件编码处理水印")

                cmd_watermark = [
                    'ffmpeg', '-y',
                    '-i', temp_step2,
                    '-vf', drawtext_filter,
                    '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                    '-crf', '18',                       # 使用CRF 18 高质量编码
                    '-preset', 'fast',                  # 平衡速度和质量
                    '-c:a', 'copy',                     # 音频直接复制，不重编码
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-copyts',                          # 复制原始时间戳，保持同步
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    temp_step3
                ]

            if log_func:
                log_func(f"[伪原创3] 一步法水印命令: {' '.join(cmd_watermark)}")

            # 执行一步法水印处理
            process = subprocess.Popen(
                cmd_watermark,
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace',
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

            # 添加进程到跟踪列表
            if parent_window and hasattr(parent_window, 'active_processes'):
                parent_window.active_processes.append(process)

            stdout, stderr = process.communicate()

            if process.returncode != 0:
                if log_func:
                    log_func(f"[伪原创3] 一步法水印处理失败: {stderr}")

                # 如果使用硬件加速失败，先尝试软件编码回退
                if use_hardware_accel:
                    if log_func:
                        log_func("[伪原创3] 硬件加速失败，尝试软件编码回退...")

                    cmd_watermark_fallback = [
                        'ffmpeg', '-y',
                        '-i', temp_step2,
                        '-vf', drawtext_filter,
                        '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                        '-crf', '18',                       # 使用CRF 18 高质量编码
                        '-preset', 'fast',                  # 平衡速度和质量
                        '-c:a', 'copy',                     # 音频直接复制，不重编码
                        '-map', '0:v:0',                    # 明确映射视频流
                        '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                        '-copyts',                          # 复制原始时间戳，保持同步
                        '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                        '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                        '-async', '1',                      # 音频同步参数
                        temp_step3
                    ]

                    if log_func:
                        log_func(f"[伪原创3] 软件编码回退命令: {' '.join(cmd_watermark_fallback)}")

                    process_fallback = subprocess.Popen(
                        cmd_watermark_fallback,
                        stderr=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

                    if parent_window and hasattr(parent_window, 'active_processes'):
                        parent_window.active_processes.append(process_fallback)

                    stdout, stderr = process_fallback.communicate()

                    if process_fallback.returncode == 0:
                        if log_func:
                            log_func("[伪原创3] 软件编码回退成功")
                        # 软件编码成功，跳过后续的字体尝试
                        font_success = True
                    else:
                        if log_func:
                            log_func(f"[伪原创3] 软件编码回退也失败: {stderr}")
                        font_success = False
                else:
                    font_success = False

                # 如果软件编码回退失败或者原本就是软件编码，尝试其他字体
                if not font_success:
                    font_success = False
                # 定义备选字体
                backup_fonts = [
                    ("C:/Windows/Fonts/simhei.ttf", "黑体"),
                    ("C:/Windows/Fonts/simsun.ttc", "宋体"),
                    ("C:/Windows/Fonts/arial.ttf", "Arial")
                ]
                
                for backup_font_path, backup_font_name in backup_fonts:
                    if not os.path.exists(backup_font_path):
                        continue

                    if log_func:
                        log_func(f"[伪原创3] 尝试备选字体: {backup_font_name} ({backup_font_path})")

                    # 修复：使用字体名称而不是路径，避免路径解析问题
                    if "msyh" in backup_font_path:
                        backup_font_name_safe = "msyh"
                    elif "simhei" in backup_font_path:
                        backup_font_name_safe = "simhei"
                    elif "simsun" in backup_font_path:
                        backup_font_name_safe = "simsun"
                    else:
                        backup_font_name_safe = "arial"

                    # 转义文本中的特殊字符
                    escaped_text_backup = chinese_text.replace("'", "\\'").replace(":", "\\:").replace("\\", "\\\\")

                    # 重新构建drawtext滤镜，使用字体名称而不是fontfile路径
                    drawtext_filter_backup = (
                        f"drawtext=font={backup_font_name_safe}:text={escaped_text_backup}:fontsize={font_size}:"
                        f"fontcolor={fontcolor}@0.8:x={chosen['x']}:y={chosen['y']}"
                    )

                    # 重新构建命令（使用修复后的同步参数）
                    cmd_watermark_backup = [
                        'ffmpeg', '-y',
                        '-i', temp_step2,
                        '-vf', drawtext_filter_backup,
                        '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                        '-crf', '18', '-preset', 'fast',
                        '-c:a', 'copy', '-map', '0:v:0', '-map', '0:a:0?',
                        '-copyts', '-avoid_negative_ts', 'disabled',
                        '-vsync', 'passthrough', '-async', '1',
                        temp_step3
                    ]

                    process_backup = subprocess.Popen(
                        cmd_watermark_backup,
                        stderr=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

                    if parent_window and hasattr(parent_window, 'active_processes'):
                        parent_window.active_processes.append(process_backup)

                    stdout, stderr = process_backup.communicate()

                    if process_backup.returncode == 0:
                        if log_func:
                            log_func(f"[伪原创3] 使用备选字体 {backup_font_name} 成功")
                        font_success = True
                        break
                    else:
                        if log_func:
                            log_func(f"[伪原创3] 备选字体 {backup_font_name} 也失败: {stderr}")

                # 如果所有字体都失败，返回错误
                if not font_success:
                    error_details = f"一步法水印处理失败（所有字体和回退方案都失败）: {stderr}"
                    status_manager.complete_step("水印处理", 3, TOTAL_STEPS, success=False, details=error_details)
                    result["success"] = False
                    result["error"] = error_details
                    return result

            # 验证输出文件
            if not os.path.exists(temp_step3):
                error_details = "一步法水印处理完成但输出文件不存在"
                status_manager.complete_step("水印处理", 3, TOTAL_STEPS, success=False, details=error_details)
                result["success"] = False
                result["error"] = error_details
                return result

            # 验证音视频同步（检查时长）
            try:
                input_duration = get_duration(temp_step2)
                output_duration = get_duration(temp_step3)
                duration_diff = abs(output_duration - input_duration)

                if log_func:
                    log_func(f"[伪原创3] ✅ 一步法音视频同步验证: 时长差异 {duration_diff:.3f}秒")
                    log_func(f"[伪原创3] 原始时长: {input_duration:.3f}秒, 输出时长: {output_duration:.3f}秒")
            except Exception as e:
                if log_func:
                    log_func(f"[伪原创3] 时长验证失败: {e}")

            if log_func:
                log_func(f"[伪原创3] ✅ 一步法水印处理完成")

        # 更新进度
        status_manager.log_progress(3, 100)

        # 更新日志
        if log_func:
            log_func("[伪原创3] Step 3/11 执行成功")
            log_func(f"[伪原创3] 生成文件: {temp_step3}")

        # 更新功能状态
        update_feature_status("伪原创3-水印处理", "成功", "水印处理已完成")
        
        # 正式完成第三步，使用状态管理器
        status_manager.complete_step("水印处理", 3, TOTAL_STEPS, success=True, details="水印处理完成", output_file=temp_step3)

        # 第4步开始前，验证第3步输出文件是否包含视频流
        streams_check = get_stream_types(temp_step3)
        has_video_check = 'video' in streams_check
            
        if not has_video_check:
            if log_func:
                log_func(f"[伪原创3] 错误: 第3步输出文件 {temp_step3} 不包含视频流！处理中止。")
                log_func(f"[伪原创3] 检测到流: {', '.join(streams_check)}")
                log_func("[伪原创3] 伪原创处理已终止，请确保处理链中的所有步骤都保留视频流。")

            # 更新功能状态
            update_feature_status("伪原创4-视频增强", "失败", "输入文件不包含视频流，处理中止")
            
            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = "输入文件不包含视频流，处理中止"
            return result

        # Step 4: 视频增强处理 - 始终执行，但RIFE补帧根据参数决定
        # 修改：使用新的文件命名约定
        temp_step4 = temp_base + ".step4.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step4)
        global_temp_files.append(temp_step4)
        
        if log_func:
            log_func("[伪原创4] Step 4/11: 视频增强处理开始...")
            log_func("[伪原创4] 进度: 0%")
            log_func(f"[伪原创4] 使用输入文件: {temp_step3}")

        # 正式开始第四步，使用状态管理器
        status_manager.start_step("视频增强", 4, TOTAL_STEPS, temp_step3)
        status_manager.log_progress(4, 0)
        
        # 使用verify_step_transition函数验证步骤之间的文件转换
        if not verify_step_transition(temp_step3, temp_step4, 3, 4, "视频增强", result):
            # 验证失败，返回结果
            return result

        # 获取界面传入的RIFE设置
        enable_step4 = True  # 总是执行Step 4
        status_manager.log_progress(4, 65)
        if enable_rife:
            log_func("[S4-视频增强] RIFE补帧功能已启用，将进行补帧处理")
        else:
            log_func("[S4-视频增强] RIFE补帧功能已禁用，将跳过补帧但执行其他视频增强")

        # 初始化变量，防止后续处理中可能未定义的变量错误
        warp_cx = random.uniform(0.0, 0.2)
        warp_cy = random.uniform(0.0, 0.2)
        warp_k1 = random.uniform(0.0, 0.1)
        warp_k2 = random.uniform(0.0, 0.1)
        speed = 1.0
        pitch_shift = 0.0
        noise_level = 0.01
        encode_output = temp_final
        
        # 应用视频增强处理 - 传递原始的enable_rife参数
        enhancement_result = apply_video_enhancement(
            temp_step3, temp_step4, log_func, enable_rife)

        status_manager.log_progress(4, 75)  # 更新进度到75%

        if enhancement_result and os.path.exists(temp_step4):
            if log_func:
                log_func("[伪原创4] 进度: 100%")
                log_func("[伪原创4] Step 4/11 执行成功")
                log_func(f"[伪原创4] 生成文件: {temp_step4}")

            # 更新功能状态
            rife_status = "已应用RIFE补帧(60fps)、" if enable_rife else ""
            update_feature_status("伪原创4-视频增强", "成功", f"{rife_status}超分辨率、HDR转换和时间超分辨率")
            
            # 第4步输出文件存在，不需要额外复制
        else:
            error_details = "视频增强处理未成功完成"
            if log_func:
                log_func(f"[伪原创4] Step 4/11 失败: {error_details}")

            # 更新功能状态
            from status_manager import update_feature_status
            update_feature_status("伪原创4-视频增强", "失败", error_details)
            
            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = error_details
            return result

        # Step 5: 特效处理（原第6步提前）
        # 第5步开始前，验证第4步输出文件是否包含视频流
        streams_check = get_stream_types(temp_step4)
        has_video_check = 'video' in streams_check
            
        if not has_video_check:
            if log_func:
                log_func(f"[伪原创4] 错误: 第4步输出文件 {temp_step4} 不包含视频流！处理中止。")
                log_func(f"[伪原创4] 检测到流: {', '.join(streams_check)}")
                log_func("[伪原创4] 伪原创处理已终止，请确保处理链中的所有步骤都保留视频流。")
            from status_manager import update_feature_status
            update_feature_status("伪原创5-特效处理", "失败", "输入文件不包含视频流，处理中止")
            result["success"] = False
            result["error"] = "输入文件不包含视频流，处理中止"
            return result
        status_manager.start_step("特效处理", 5, TOTAL_STEPS, temp_step4)
        status_manager.log_progress(5, 0)
        if log_func:
            log_func("[伪原创5] Step 5/11: 特效处理开始...")
            log_func(f"[伪原创5] 使用输入文件: {temp_step4}")
        temp_step5 = temp_base + ".step5.mp4"
        add_temp_file(temp_step5)
        global_temp_files.append(temp_step5)
        if not verify_step_transition(temp_step4, temp_step5, 4, 5, "特效处理", result):
            return result
        effects_config = {}
        effect_candidates = []
        particle_effect_type = "无"
        particle_effect_intensity = 0
        light_effect_type = "无"
        light_effect_intensity = 0
        transition_effect_type = "无"
        transition_effect_direction = "无"
        text_border_enabled = False
        text_border_color = "无"
        text_border_width = 0
        if random.random() < 0.2:
            particle_types = ["snow", "rain", "sparkle", "dust"]
            particle_effect_type = random.choice(particle_types)
            particle_effect_intensity = random.uniform(0.1, 0.3)
            effects_config["particle_effect"] = {
                "type": particle_effect_type,
                "duration": random.uniform(5, 15),
                "intensity": particle_effect_intensity
            }
            effect_candidates.append("particle_effect")
            if log_func:
                log_func(f"[伪原创] Step 5/11 选择粒子效果: {particle_effect_type}")
        if random.random() < 0.15:
            light_types = ["lens_flare", "beam", "glow", "flash"]
            light_effect_type = random.choice(light_types)
            light_effect_intensity = random.uniform(0.1, 0.3)
            effects_config["light_effect"] = {
                "type": light_effect_type,
                "duration": random.uniform(3, 10),
                "intensity": light_effect_intensity
            }
            effect_candidates.append("light_effect")
            if log_func:
                log_func(f"[伪原创] Step 5/11 选择光效: {light_effect_type}")
        # 完全禁用转场效果，避免黑屏问题
        if False:  # 禁用所有转场效果
            transition_types = ["slide", "zoom", "wipe"]  # 移除"fade"
            transition_effect_type = random.choice(transition_types)
            # 只允许"out"方向，禁用"in"方向（开场效果）
            transition_effect_direction = "out"  # 强制使用结尾效果
            effects_config["transition_effect"] = {
                "type": transition_effect_type,
                "duration": random.uniform(1, 3),
                "direction": transition_effect_direction
            }
            effect_candidates.append("transition_effect")
            if log_func:
                log_func(f"[伪原创] Step 5/11 选择转场效果: {transition_effect_type} (仅结尾)")
        if random.random() < 0.15:
            text_border_enabled = True
            border_colors = [
                "white", "black", "red", "blue", "green", "yellow", "orange", "purple"]
            text_border_color = random.choice(border_colors)
            text_border_width = random.randint(1, 4)
            effects_config["text_border"] = {
                "color": text_border_color,
                "width": text_border_width
            }
            effect_candidates.append("text_border")
            if log_func:
                log_func(f"[伪原创] Step 5/11 选择文字边框: 颜色={text_border_color}, 宽度={text_border_width}px")
        all_effects = ["particle_effect", "light_effect", "transition_effect", "text_border"]
        while len(effect_candidates) < 2:
            remaining = [e for e in all_effects if e not in effect_candidates]
            if not remaining:
                break
            effect = random.choice(remaining)
            if effect == "particle_effect":
                particle_types = ["snow", "rain", "sparkle", "dust"]
                particle_effect_type = random.choice(particle_types)
                particle_effect_intensity = random.uniform(0.1, 0.3)
                effects_config["particle_effect"] = {
                    "type": particle_effect_type,
                    "duration": random.uniform(5, 15),
                    "intensity": particle_effect_intensity
                }
                effect_candidates.append("particle_effect")
                if log_func:
                    log_func(f"[伪原创] Step 5/11 强制选择粒子效果: {particle_effect_type}")
            elif effect == "light_effect":
                light_types = ["lens_flare", "beam", "glow", "flash"]
                light_effect_type = random.choice(light_types)
                light_effect_intensity = random.uniform(0.1, 0.3)
                effects_config["light_effect"] = {
                    "type": light_effect_type,
                    "duration": random.uniform(3, 10),
                    "intensity": light_effect_intensity
                }
                effect_candidates.append("light_effect")
                if log_func:
                    log_func(f"[伪原创] Step 5/11 强制选择光效: {light_effect_type}")
            elif effect == "transition_effect":
                # 完全禁用转场效果，避免黑屏问题
                if log_func:
                    log_func(f"[伪原创] Step 5/11 跳过转场效果（已禁用避免黑屏）")
                # 不添加转场效果到配置中
            elif effect == "text_border":
                text_border_enabled = True
                border_colors = [
                    "white", "black", "red", "blue", "green", "yellow", "orange", "purple"]
                text_border_color = random.choice(border_colors)
                text_border_width = random.randint(1, 4)
                effects_config["text_border"] = {
                    "color": text_border_color,
                    "width": text_border_width
                }
                effect_candidates.append("text_border")
                if log_func:
                    log_func(f"[伪原创] Step 5/11 强制选择文字边框: 颜色={text_border_color}, 宽度={text_border_width}px")
        if effects_config:
            status_manager.log_progress(5, 30)
            if log_func:
                log_func(f"[伪原创5] 应用特效: {effects_config}")
            try:
                effects_success = apply_special_effects(temp_step4, temp_step5, effects_config, log_func)
            except Exception as e:
                effects_success = False
                if log_func:
                    log_func(f"[伪原创5] 特效处理异常: {str(e)}")
                    import traceback
                    log_func(f"[伪原创5] 异常详情: {traceback.format_exc()}")
            if effects_success:
                status_manager.log_progress(5, 100)
                if log_func:
                    log_func(f"[伪原创5] 生成文件: {temp_step5}")
                    log_func(f"[伪原创] Step 5/11 完成：特效处理已应用")
                success_details = f"特效数量: {len(effects_config)}"
                from status_manager import update_feature_status
                update_feature_status("伪原创5-特效处理", "成功", success_details)
                status_manager.complete_step("特效处理", 5, TOTAL_STEPS, success=True, details=success_details, output_file=temp_step5)
            else:
                status_manager.log_progress(5, 100)
                error_details = "特效应用失败"
                if log_func:
                    log_func(f"[伪原创5] {error_details}，处理中止")
                from status_manager import update_feature_status
                update_feature_status("伪原创5-特效处理", "失败", error_details)
                status_manager.complete_step("特效处理", 5, TOTAL_STEPS, success=False, details=error_details, output_file=None)
                result["success"] = False
                result["error"] = error_details
                return result
        else:
            import shutil
            shutil.copy(temp_step4, temp_step5)
            status_manager.log_progress(5, 100)
            if log_func:
                log_func(f"[伪原创5] 无特效配置，直接复制文件")
                log_func(f"[伪原创5] 生成文件: {temp_step5}")
                log_func(f"[伪原创5] 完成：无特效处理")
            success_details = "无特效应用"
            from status_manager import update_feature_status
            update_feature_status("伪原创5-特效处理", "成功", success_details)
            status_manager.complete_step("特效处理", 5, TOTAL_STEPS, success=True, details=success_details, output_file=temp_step5)
        streams_check = get_stream_types(temp_step5)
        has_video_check = 'video' in streams_check
        if not has_video_check:
            if log_func:
                log_func(f"[伪原创] Step 5/11 失败：输出文件 {temp_step5} 不包含视频流！")
            from status_manager import update_feature_status
            update_feature_status("伪原创5-特效处理", "失败", f"输出文件 {temp_step5} 不包含视频流")
            result["success"] = False
            result["error"] = f"输出文件 {temp_step5} 不包含视频流"
            return result

        # Step 6: 动态装饰效果（与原第7步交换）
        status_manager.start_step("动态装饰效果", 6, TOTAL_STEPS, temp_step5)
        status_manager.log_progress(6, 0)
        if log_func:
            log_func("[伪原创] Step 6/11: 动态装饰效果开始...")
            log_func("[伪原创6] 进度: 0%")
            log_func(f"[伪原创6] 使用输入文件: {temp_step5}")
        temp_step6 = temp_base + ".step6.mp4"
        add_temp_file(temp_step6)
        global_temp_files.append(temp_step6)
        if not verify_step_transition(temp_step5, temp_step6, 5, 6, "动态装饰效果", result):
            return result
        # 调用装饰效果处理函数
        decoration_success = execute_decorations_as_step6(temp_step5, temp_step6, log_func, status_manager)

        if not decoration_success:
            error_details = "动态装饰效果处理失败"
            if log_func:
                log_func(f"[伪原创] 错误：Step 6动态装饰效果处理失败，处理中止")

            # 更新功能状态
            from status_manager import update_feature_status
            update_feature_status("伪原创6-动态装饰效果", "失败", error_details)

            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = error_details
            return result

        # 检查输出文件是否存在
        if not os.path.exists(temp_step6):
            error_details = "动态装饰效果处理失败，输出文件不存在"
            if log_func:
                log_func(f"[伪原创] 错误：Step 6输出文件不存在，处理中止")

            # 更新功能状态
            from status_manager import update_feature_status
            update_feature_status("伪原创6-动态装饰效果", "失败", error_details)

            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = error_details
            return result
        # 第6步（动态装饰效果）已完成，继续下一步
        # 继续执行Step 7 - 横屏转竖屏处理（与原第6步交换）
        status_manager.start_step("横屏转竖屏", 7, TOTAL_STEPS, temp_step6)
        status_manager.log_progress(7, 0)
        if log_func:
            log_func("[伪原创] Step 7/11: 横屏转竖屏处理开始...")
            log_func(f"[伪原创7] 使用输入文件: {temp_step6}")
        temp_step7 = temp_base + ".step7.mp4"
        add_temp_file(temp_step7)
        global_temp_files.append(temp_step7)
        if not verify_step_transition(temp_step6, temp_step7, 6, 7, "横屏转竖屏", result):
            return result

        enable_landscape_to_portrait = step5_params.get('portrait_to_landscape', True)
        if enable_landscape_to_portrait:
            if log_func:
                log_func(f"[伪原创7] 将执行横屏转竖屏处理")
        else:
            if log_func:
                log_func(f"[伪原创7] 将保持原始视频方向")

        # 调用横屏转竖屏处理函数
        try:
            # 实现完整的横屏转竖屏处理逻辑
            portrait_success = execute_portrait_conversion(temp_step6, temp_step7, enable_landscape_to_portrait, log_func, overlay_text)

            if portrait_success:
                if log_func:
                    log_func("[伪原创] Step 7/11 横屏转竖屏处理完成")

                # 更新功能状态，包含详细信息
                from status_manager import update_feature_status
                operation_type = "横屏转竖屏" if enable_landscape_to_portrait else "保持原始方向"
                success_details = f"{operation_type}, 目标分辨率=1080x1920, 文案添加=是"
                update_feature_status("伪原创7-横屏转竖屏", "成功", success_details)

                # 正式完成第七步，使用状态管理器
                status_manager.complete_step("横屏转竖屏", 7, TOTAL_STEPS, success=True, details=success_details, output_file=temp_step7)
            else:
                raise Exception("横屏转竖屏处理失败")

        except Exception as e:
            if log_func:
                log_func(f"[伪原创] Step 7/11 横屏转竖屏处理失败: {str(e)}")

            error_details = "横屏转竖屏处理失败"
            from status_manager import update_feature_status
            update_feature_status("伪原创7-横屏转竖屏", "失败", error_details)

            result["success"] = False
            result["error"] = error_details
            return result
        # 检查输出文件是否存在
        if not os.path.exists(temp_step7):
            error_details = "横屏转竖屏处理失败，输出文件不存在"
            if log_func:
                log_func(f"[伪原创] 错误：Step 7输出文件不存在，处理中止")

            # 更新功能状态
            from status_manager import update_feature_status
            update_feature_status("伪原创7-横屏转竖屏", "失败", error_details)

            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = error_details
            return result
        # 继续执行Step 8 - 音频混音处理
        # 第6步和第7步交换完成，继续第8步
        # 所有遗留的原第6步代码已删除
        # 所有遗留的原第6步横屏转竖屏代码已删除，现在移到第7步
        # ========== Step 8: 音频混音处理 ==========
        # 第8步必须使用第7步的输出，如果不存在则停止处理
        if not os.path.exists(temp_step7):
            error_details = "第7步输出文件不存在，无法继续第8步处理"
            if log_func:
                log_func(f"[伪原创8] 错误：{error_details}")

            # 更新功能状态
            update_feature_status("伪原创8-音频混音处理", "失败", error_details)

            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = error_details
            return result

        audio_input_check = temp_step7
            
        streams_check = get_stream_types(audio_input_check)
        has_video_check = 'video' in streams_check
            
        if not has_video_check:
            if log_func:
                log_func(f"[伪原创] 错误: 第7步输出文件 {audio_input_check} 不包含视频流！处理中止。")
                log_func(f"[伪原创] 检测到流: {', '.join(streams_check)}")
                log_func("[伪原创] 伪原创处理已终止，请确保处理链中的所有步骤都保留视频流。")
            
            # 更新功能状态
            update_feature_status("伪原创8-音频混音处理", "失败", "输入文件不包含视频流，处理中止")
            
            # 清理临时文件并终止处理
            result["success"] = False
            result["error"] = "输入文件不包含视频流，处理中止"
            return result
        
        if log_func:
            log_func("[伪原创] Step 8/11: 音频混音处理开始...")
            log_func("[伪原创8] 进度: 0%")
            log_func(f"[伪原创8] 使用输入文件: {audio_input_check}")

        # 获取输入文件信息
        try:
            file_info = status_manager.get_media_info(audio_input_check)
            if file_info and file_info.get("is_valid") and log_func:
                log_func(f"[伪原创8] 输入文件信息:")
                log_func(f"[伪原创8]   - 尺寸: {file_info.get('resolution', 'unknown')}")
                log_func(f"[伪原创8]   - 时长: {file_info.get('duration', 'unknown')}秒")
                log_func(f"[伪原创8]   - 编码: {file_info.get('video_codec', 'unknown')}")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创8] 获取输入文件信息失败: {str(e)}")
        
        # 正式开始第八步，使用状态管理器
        status_manager.start_step("音频混音处理", 8, TOTAL_STEPS, audio_input_check)
        status_manager.log_progress(8, 0)

        # 确定音频处理的输入文件
        audio_input_file = audio_input_check  # 使用前面验证过的文件

        # 设置第八步的输出文件路径
        temp_step8 = temp_base + ".step8.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step8)
        global_temp_files.append(temp_step8)

        # 在应用音频处理前验证步骤转换
        if not verify_step_transition(audio_input_file, temp_step8, 7, 8, "音频混音处理", result):
            # 验证失败，返回结果
            result["success"] = False
            result["error"] = "步骤转换验证失败"
            return result
        # 音频临时文件（添加递增序号后缀避免冲突）
        import os
        import glob

        # 查找现有的音频文件，获取最大序号
        # 获取输出目录，在该目录下搜索现有的音频文件
        output_dir = os.path.dirname(temp_base)
        base_filename = os.path.basename(temp_base)

        # 扩大搜索范围，查找所有可能的音频文件模式
        search_patterns = [
            f"{base_filename}.audio_*.wav",
            f"{base_filename}.audio_vocals_*.wav",
            f"{base_filename}.audio_accompaniment_*.wav",
            f"{base_filename}.audio_with_bgm_*.wav",
            f"{base_filename}.audio_processed_*.wav",
            f"{base_filename}.audio_mixed_*.wav"
        ]

        max_number = 0
        for pattern in search_patterns:
            full_pattern = os.path.join(output_dir, pattern)
            existing_files = glob.glob(full_pattern)
            if log_func and existing_files:
                log_func(f"[伪原创8] 找到现有音频文件: {[os.path.basename(f) for f in existing_files]}")

            for file in existing_files:
                try:
                    # 提取文件名中的序号
                    basename = os.path.basename(file)
                    # 查找所有可能的音频文件模式中的序号
                    for audio_type in ["audio_", "audio_vocals_", "audio_accompaniment_", "audio_with_bgm_", "audio_processed_", "audio_mixed_"]:
                        if audio_type in basename:
                            parts = basename.split(audio_type)
                            if len(parts) > 1:
                                number_part = parts[1].split(".wav")[0]
                                if number_part.isdigit():
                                    current_number = int(number_part)
                                    max_number = max(max_number, current_number)
                                    if log_func:
                                        log_func(f"[伪原创8] 从文件 {basename} 提取序号: {current_number}")
                                break
                except Exception as e:
                    if log_func:
                        log_func(f"[伪原创8] 解析文件名失败: {basename}, 错误: {e}")
                    continue

        # 生成新的序号（从001开始）
        sequence_number = max_number + 1
        sequence_suffix = f"{sequence_number:03d}"  # 格式化为3位数字，如001, 002, 003

        if log_func:
            log_func(f"[伪原创8] 使用Step 7的输出作为输入: {audio_input_file}")
            log_func(f"[伪原创8] 检测到已有音频文件最大序号: {max_number}")
            log_func(f"[伪原创8] 音频处理序号: {sequence_suffix}")
            log_func(f"[伪原创8] 音频文件命名规则: step8_步骤名称_{sequence_suffix}.wav")

        # 使用更清晰的命名方式：步骤名称_序号
        temp_audio = temp_base + f".step8_1_原始音频_{sequence_suffix}.wav"                      # 步骤1：原始提取音频
        temp_audio_vocals = temp_base + f".step8_2_人声分离_{sequence_suffix}.wav"        # 步骤2：人声分离后的音频
        temp_audio_vocals_balanced = temp_base + f".step8_3_人声响度平衡_{sequence_suffix}.wav"  # 步骤3：人声响度平衡
        temp_audio_accompaniment = temp_base + f".step8_4_背景音乐_{sequence_suffix}.wav"  # 步骤4：背景音乐分离后的音频
        temp_audio_with_bgm = temp_base + f".step8_5_新背景混音_{sequence_suffix}.wav"    # 步骤5：人声+新背景音乐混音
        temp_audio_processed = temp_base + f".step8_6_音频处理_{sequence_suffix}.wav"  # 步骤6：音频处理（变速、音高、噪声）
        temp_audio_mixed = temp_base + f".step8_7_最终混音_{sequence_suffix}.wav"          # 步骤7：最终处理后的音频

        # 添加临时音频文件到跟踪列表
        add_temp_file(temp_audio)
        add_temp_file(temp_audio_vocals)
        add_temp_file(temp_audio_vocals_balanced)
        add_temp_file(temp_audio_accompaniment)
        add_temp_file(temp_audio_with_bgm)
        add_temp_file(temp_audio_processed)
        add_temp_file(temp_audio_mixed)
        global_temp_files.extend([temp_audio, temp_audio_vocals, temp_audio_vocals_balanced, temp_audio_accompaniment,
                                 temp_audio_with_bgm, temp_audio_processed, temp_audio_mixed])
        temp_audio_output = temp_step8  # 使用标准化的输出路径

        # 检查输入文件是否包含音频流
        streams_info = get_stream_types(audio_input_file)
        has_audio = 'audio' in streams_info

        if not has_audio:
            if log_func:
                log_func(f"[伪原创8] 输入文件不包含音频流，跳过音频处理")
                log_func(f"[伪原创8] 检测到流类型: {', '.join(streams_info)}")
                log_func(f"[伪原创8] 直接复制视频文件到输出")

            # 直接复制视频文件，跳过音频处理
            import shutil
            try:
                shutil.copy2(audio_input_file, temp_step8)
                if log_func:
                    log_func(f"[伪原创8] 文件复制成功: {temp_step8}")
                    log_func("[伪原创8] Step 8/11 执行成功（跳过音频处理）")

                # 更新功能状态
                update_feature_status("伪原创8-音频混音处理", "成功", "跳过音频处理（无音频流）")
                status_manager.complete_step("音频混音处理", 8, TOTAL_STEPS, success=True,
                                           details="跳过音频处理（无音频流）", output_file=temp_step8)

            except Exception as copy_error:
                if log_func:
                    log_func(f"[伪原创8] 文件复制失败: {copy_error}")
                result["success"] = False
                result["error"] = f"文件复制失败: {copy_error}"
                return result
        else:
            # 步骤1：提取音频
            extract_cmd = [
                'ffmpeg', '-y', '-i', audio_input_file, '-vn', '-acodec', 'pcm_s16le', temp_audio
            ]

            if log_func:
                log_func(f"[伪原创8] 步骤1/7: 提取原始音频...")
                log_func(f"[伪原创8] 提取音频: {' '.join(extract_cmd)}")

            process_extract = run_subprocess(extract_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

            if process_extract.returncode != 0:
                if log_func:
                    log_func(f"[伪原创8] 音频提取失败: {process_extract.stderr}")
                # 如果提取失败，终止处理
                error_details = "音频提取失败"
                if log_func:
                    log_func(f"[伪原创8] 错误：{error_details}，处理中止")

                # 更新失败状态
                update_feature_status("伪原创8-音频混音处理", "失败", error_details)

                # 正式完成第八步，使用状态管理器（失败状态）
                status_manager.complete_step("音频混音处理", 8, TOTAL_STEPS, success=False, details=error_details, output_file=None)

                # 清理临时文件并终止处理
                result["success"] = False
                result["error"] = error_details
                return result

            if log_func:
                log_func(f"[伪原创8] ✅ 步骤1完成: 音频提取成功 -> {os.path.basename(temp_audio)}")

            # 步骤2：使用AI进行人声分离
            if log_func:
                log_func(f"[伪原创8] 步骤2/7: 开始人声分离...")
                
            # 获取人声分离次数设置
            if vocal_sep_count < 1:
                vocal_sep_count = 1  # 确保至少执行一次
            if vocal_sep_count > 1 and log_func:
                log_func(f"[伪原创8] 将执行 {vocal_sep_count} 次人声分离，以获得更纯净的人声")

            vocals_input = temp_audio  # 默认使用原始音频

            # 使用系统安装的官方版本Spleeter进行AI人声分离
            if log_func:
                log_func(f"[伪原创8] 使用Python 3.8环境中安装的官方版本Spleeter进行AI人声分离...")

            # 使用Python 3.8虚拟环境中已安装的官方Spleeter
            venv_python = os.path.join(os.path.dirname(__file__), "venv38", "Scripts", "python.exe")
            
            # 如果Python 3.8虚拟环境存在，使用它
            if os.path.exists(venv_python):
                if log_func:
                    log_func(f"[伪原创8] 使用Python 3.8环境中的官方Spleeter...")

                try:
                    # 用于存储多次人声分离的结果
                    vocals_files = []
                    spleeter_output_dirs = []
                    
                    # 设置模型路径
                    model_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pretrained_models")
                    if log_func:
                        log_func(f"[伪原创8] 指定模型加载路径: {model_path}")
                    
                    # 通过环境变量指定模型路径，兼容Spleeter 2.4.0
                    os.environ["MODEL_PATH"] = model_path
                    if log_func:
                        log_func(f"[伪原创8] 通过环境变量设置模型路径: MODEL_PATH={model_path}")
                        log_func(f"[伪原创8] 模型文件位置详情:")
                        
                        # 检查模型文件是否存在
                        stems_models = {
                            "2stems": os.path.join(model_path, "2stems"),
                            "4stems": os.path.join(model_path, "4stems"),
                            "5stems": os.path.join(model_path, "5stems")
                        }
                        
                        for model_name, model_dir in stems_models.items():
                            if os.path.exists(model_dir):
                                log_func(f"[伪原创8] ✅ 找到{model_name}模型目录: {model_dir}")
                                # 检查关键文件
                                checkpoint_file = os.path.join(model_dir, "checkpoint")
                                if os.path.exists(checkpoint_file):
                                    log_func(f"[伪原创8]   - 找到checkpoint文件")
                                    # 读取checkpoint文件内容
                                    try:
                                        with open(checkpoint_file, 'r') as f:
                                            checkpoint_content = f.read()
                                            log_func(f"[伪原创8]   - Checkpoint信息: {checkpoint_content.strip()}")
                                    except Exception as e:
                                        log_func(f"[伪原创8]   - 无法读取checkpoint文件: {str(e)}")
                                else:
                                    log_func(f"[伪原创8]   - ❌ 缺少checkpoint文件!")
                                    
                                # 检查模型文件
                                model_files = [f for f in os.listdir(model_dir) if f.startswith("model") and (f.endswith(".data-00000-of-00001") or f.endswith(".index") or f.endswith(".meta"))]
                                if model_files:
                                    log_func(f"[伪原创8]   - 找到模型文件: {', '.join(model_files)}")
                                else:
                                    log_func(f"[伪原创8]   - ❌ 缺少必要模型文件!")
                            else:
                                log_func(f"[伪原创8] ❌ 未找到{model_name}模型目录")
                    
                    # 将构建和执行Spleeter命令的代码移到循环内部

                    # 使用Popen替代run，以便实时获取输出并显示进度
                    try:
                        import time
                        import threading
                        
                        # 逐次执行多次人声分离 - 添加详细日志
                        if log_func:
                            log_func(f"[伪原创8] 【诊断】传入的人声分离次数参数值: {vocal_sep_count}")
                            # 检查配置中的值
                            try:
                                import config_manager
                                config_vocal_sep_count = config_manager.get_config().get('vocal_sep_count', 1)
                                log_func(f"[伪原创8] 【诊断】配置中的人声分离次数: {config_vocal_sep_count}")
                            except Exception as e:
                                log_func(f"[伪原创8] 【诊断】获取配置失败: {e}")
                        
                        # 确保使用正确的人声分离次数
                        # 检查函数参数值是否有效
                        if vocal_sep_count is None or vocal_sep_count <= 0:
                            # 如果参数无效，尝试从配置中获取
                            try:
                                import config_manager
                                config_vocal_sep_count = config_manager.get_config().get('vocal_sep_count', 1)
                                if config_vocal_sep_count > 0:
                                    actual_vocal_sep_count = config_vocal_sep_count
                                    if log_func:
                                        log_func(f"[伪原创8] 传入的人声分离次数无效，使用配置中的值: {actual_vocal_sep_count}")
                                else:
                                    # 如果配置值也无效，使用默认值1
                                    actual_vocal_sep_count = 1
                                    if log_func:
                                        log_func(f"[伪原创8] 配置中的人声分离次数无效，使用默认值: 1")
                            except Exception as e:
                                # 如果获取配置失败，使用默认值1
                                actual_vocal_sep_count = 1
                                if log_func:
                                    log_func(f"[伪原创8] 获取配置失败，使用默认值: 1, 错误: {e}")
                        else:
                            # 使用传入的参数值
                            actual_vocal_sep_count = vocal_sep_count
                            if log_func:
                                log_func(f"[伪原创8] 使用传入的人声分离次数: {actual_vocal_sep_count}")
                        
                        if log_func:
                            log_func(f"[伪原创8] 开始执行人声分离循环，计划执行次数: {actual_vocal_sep_count}")
                            
                        for sep_idx in range(actual_vocal_sep_count):
                            # 确保在正确的时机检查vocals_files
                            # 只有在完成上一次分离后再检查
                            if sep_idx > 0:
                                # 添加检查，确保非第一次分离时vocals_files不为空
                                if not vocals_files:
                                    if log_func:
                                        log_func(f"[伪原创8] ⚠️ 前一次分离失败，没有有效的人声文件，终止后续分离")
                                    break  # 中断循环，不执行后续分离
                                else:
                                    if log_func:
                                        log_func(f"[伪原创8] 第{sep_idx}次分离成功，继续执行第{sep_idx+1}次分离")
                                
                            current_input = temp_audio if sep_idx == 0 else vocals_files[-1]
                            
                            # 创建输出目录，第1次以后的目录名加上索引号
                            current_spleeter_output_dir = os.path.join(
                                os.path.dirname(temp_audio), 
                                f"spleeter_output{'' if sep_idx == 0 else str(sep_idx + 1)}"
                            )
                            os.makedirs(current_spleeter_output_dir, exist_ok=True)
                            spleeter_output_dirs.append(current_spleeter_output_dir)
                            
                            if log_func and sep_idx > 0:
                                log_func(f"[伪原创8] 开始第 {sep_idx + 1} 次人声分离...")
                                log_func(f"[伪原创8] 使用上一次分离得到的人声文件作为输入: {os.path.basename(current_input)}")
                            
                            if log_func:
                                # 修改日志，添加总次数信息
                                if sep_idx > 0:
                                    log_func(f"[伪原创8] 执行第 {sep_idx + 1}/{vocal_sep_count} 次人声分离... (总共设置为: {vocal_sep_count}次)")
                                else:
                                    log_func(f"[伪原创8] 执行第 1/{vocal_sep_count} 次人声分离... (总共设置为: {vocal_sep_count}次)")
                            
                            # 构建Spleeter命令 - 使用官方版本，优先使用2stems模型（更稳定）
                            spleeter_cmd = [
                                venv_python,
                                "-m", "spleeter",
                                "separate",
                                "-p", "spleeter:2stems-16kHz",  # 使用更稳定的2stems模型
                                "-o", current_spleeter_output_dir,
                                current_input
                            ]

                            # 设置GPU兼容性环境变量 - 解决CUDA 12.x与TensorFlow 2.9.x兼容性问题
                            env = os.environ.copy()
                            env.update({
                                # GPU兼容性核心设置
                                'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
                                'TF_GPU_ALLOCATOR': 'cuda_malloc_async',
                                'CUDA_MODULE_LOADING': 'LAZY',
                                'TF_CUDA_COMPUTE_CAPABILITIES': '8.6',  # RTX 3060的计算能力

                                # 内存管理优化
                                'TF_GPU_THREAD_MODE': 'gpu_private',
                                'TF_GPU_THREAD_COUNT': '2',

                                # 减少日志输出
                                'TF_CPP_MIN_LOG_LEVEL': '1',

                                # 兼容性模式设置
                                'TF_ENABLE_ONEDNN_OPTS': '0',
                                'TF_DISABLE_MKL': '1',
                                'CUDA_CACHE_DISABLE': '0',
                                'TF_ENABLE_GPU_GARBAGE_COLLECTION': 'true'
                            })

                            # 确保使用GPU（移除可能的CPU强制设置）
                            if 'CUDA_VISIBLE_DEVICES' in env and env['CUDA_VISIBLE_DEVICES'] == '-1':
                                del env['CUDA_VISIBLE_DEVICES']

                            if log_func:
                                log_func(f"[伪原创8] 执行官方Spleeter命令: {' '.join(spleeter_cmd)}")
                                log_func(f"[伪原创8] ⚠️ 使用2stems稳定模型+GPU优化，处理时间约1-2分钟...")
                                log_func(f"[伪原创8] 🚀 GPU优化已启用：内存增长、异步分配器、垃圾回收")

                            # 创建Popen对象并捕获输出
                            process = subprocess.Popen(
                                spleeter_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                universal_newlines=True,
                                encoding='utf-8',
                                errors='replace',
                                env=env,  # 使用GPU优化的环境变量
                                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0
                            )
                        
                        # 添加进程到跟踪列表
                        if 'parent_window' in locals() and parent_window and hasattr(parent_window, 'active_processes'):
                            parent_window.active_processes.append(process)
                        
                        # 定义一个读取输出的函数
                        def read_output(pipe, prefix, process_log_func):
                            for line in iter(pipe.readline, ''):
                                if process_log_func and line.strip():
                                    # 过滤有用的信息
                                    if ('stem' in line.lower() or 'progress' in line.lower() or 
                                        'loading model' in line.lower() or 'error' in line.lower() or 
                                        'warning' in line.lower() or 'tensorflow' in line.lower()):
                                        process_log_func(f"[伪原创8] {prefix}: {line.strip()}")
                            if pipe.closed:
                                return
                        
                        # 创建线程读取stdout和stderr
                        stdout_thread = threading.Thread(target=read_output, args=(process.stdout, "进度", log_func))
                        stderr_thread = threading.Thread(target=read_output, args=(process.stderr, "状态", log_func))
                        
                        # 设置为守护线程并启动
                        stdout_thread.daemon = True
                        stderr_thread.daemon = True
                        stdout_thread.start()
                        stderr_thread.start()
                        
                        # 每10秒输出一次处理状态
                        start_time = time.time()
                        dots = 1
                        progress_phases = ["加载模型", "前处理", "分离人声", "分离鼓点", "分离贝斯", "分离钢琴", "分离其他", "后处理", "保存文件"]
                        current_phase_index = 0
                        last_output_time = 0
                        
                        while process.poll() is None:
                            time.sleep(2)  # 每2秒检查一次
                            elapsed = time.time() - start_time
                            current_time = time.time()
                            
                            # 根据已用时间估算处理阶段
                            if elapsed > 10:
                                if elapsed < 30:
                                    current_phase_index = 0  # 加载模型
                                elif elapsed < 60:
                                    current_phase_index = 1  # 前处理
                                elif elapsed < 120:
                                    current_phase_index = 2  # 分离人声
                                elif elapsed < 150:
                                    current_phase_index = 3  # 分离鼓点
                                elif elapsed < 180:
                                    current_phase_index = 4  # 分离贝斯
                                elif elapsed < 210:
                                    current_phase_index = 5  # 分离钢琴
                                elif elapsed < 240:
                                    current_phase_index = 6  # 分离其他
                                else:
                                    current_phase_index = 7  # 后处理或保存文件
                            
                            # 显示进度指示器
                            if elapsed > 10 and log_func and (current_time - last_output_time) >= 10:  # 每10秒输出一次
                                last_output_time = current_time
                                progress_dots = '.' * dots
                                dots = dots % 3 + 1  # 在1-3之间循环
                                
                                estimated_progress = min(95, int(elapsed / 300 * 100))  # 假设最多5分钟完成
                                current_phase = progress_phases[min(current_phase_index, len(progress_phases)-1)]
                                
                                log_func(f"[伪原创8] Spleeter处理中{progress_dots} 已用时{elapsed:.1f}秒 | 当前估计阶段: {current_phase} | 大约{estimated_progress}%完成")
                                
                                # 检查内存使用情况
                                try:
                                    import psutil
                                    process_obj = psutil.Process(process.pid)
                                    mem_usage = process_obj.memory_info().rss / (1024 * 1024)  # MB
                                    log_func(f"[伪原创8] 当前内存占用: {mem_usage:.1f} MB")
                                    
                                    # 检查是否文件已经生成（提前完成的迹象）
                                    audio_name = os.path.splitext(os.path.basename(temp_audio))[0]
                                    vocals_file = os.path.join(current_spleeter_output_dir, audio_name, "vocals.wav")
                                    if os.path.exists(vocals_file):
                                        file_size = os.path.getsize(vocals_file) / (1024*1024)  # MB
                                        log_func(f"[伪原创8] 检测到vocals.wav已生成! 文件大小: {file_size:.2f}MB")
                                        
                                except:
                                    pass  # 忽略检查错误
                        
                        # 修复版: 将communicate移到循环外部，等待进程正确结束
                        stdout, stderr = process.communicate()
                        returncode = process.returncode  # 进程结束后returncode必然有值
                        
                        if returncode == 0:
                            if log_func:
                                log_func(f"[伪原创8] ✅ Spleeter执行完成! 用时: {time.time() - start_time:.1f}秒")
                            
                        # 增强多次人声分离的日志和文件处理
                        # 获取当前音频名称
                        current_audio_name = os.path.splitext(os.path.basename(current_input))[0]
                        # 根据当前输出目录和音频名获取vocals文件路径
                        current_vocals_file = os.path.join(current_spleeter_output_dir, current_audio_name, "vocals.wav")

                        # 检查文件是否存在
                        if os.path.exists(current_vocals_file):
                            # 打印文件信息用于调试
                            file_size_mb = os.path.getsize(current_vocals_file) / (1024 * 1024)
                            if log_func:
                                log_func(f"[伪原创8] 找到分离后的vocals文件: {current_vocals_file}")
                                log_func(f"[伪原创8] vocals文件大小: {file_size_mb:.2f}MB")

                            # 将vocals文件复制到一个标准路径，确保下次循环能找到
                            next_iter_vocals_file = os.path.join(os.path.dirname(temp_audio), f"vocals_iter_{sep_idx+1}.wav")
                            import shutil
                            shutil.copy2(current_vocals_file, next_iter_vocals_file)

                            # 确认复制后文件存在
                            if os.path.exists(next_iter_vocals_file):
                                if log_func:
                                    log_func(f"[伪原创8] ✅ 成功复制vocals文件到: {next_iter_vocals_file}")
                                    log_func(f"[伪原创8] 文件复制后大小: {os.path.getsize(next_iter_vocals_file)/(1024*1024):.2f}MB")
                            else:
                                if log_func:
                                    log_func(f"[伪原创8] ❌ 复制vocals文件失败!")

                            # 检查人声文件是否有效
                            vocals_valid = os.path.exists(next_iter_vocals_file) and os.path.getsize(next_iter_vocals_file) > 50000

                            if vocals_valid:
                                        # 只有当人声文件有效时才添加到列表中
                                        vocals_files.append(next_iter_vocals_file)
                                        
                                        if sep_idx < vocal_sep_count-1:  # 如果不是最后一次分离
                                            if log_func:
                                                log_func(f"[伪原创8] 🔄 已添加第 {sep_idx+1} 次分离的人声文件: {os.path.basename(next_iter_vocals_file)}")
                                                log_func(f"[伪原创8] 🔄 vocals_files列表现在包含 {len(vocals_files)} 个文件")
                                                log_func(f"[伪原创8] 🔄 下一循环将使用此文件作为输入: {next_iter_vocals_file}")
                            else:
                                if log_func:
                                    log_func(f"[伪原创8] ⚠️ 警告: 分离的人声文件无效或过小，不会进行后续分离")
                                    if not os.path.exists(next_iter_vocals_file):
                                        log_func(f"[伪原创8]   - 文件不存在: {next_iter_vocals_file}")
                                    else:
                                        log_func(f"[伪原创8]   - 文件过小: {os.path.getsize(next_iter_vocals_file)/1024:.1f}KB")
                                else:
                                    if log_func:
                                        log_func(f"[伪原创8] ⚠️ 严重警告: 未找到分离后的vocals文件: {current_vocals_file}")
                                        log_func(f"[伪原创8] 📂 目录内容:")
                                        try:
                                            output_dir = os.path.join(current_spleeter_output_dir, current_audio_name)
                                            if os.path.exists(output_dir):
                                                for f in os.listdir(output_dir):
                                                    log_func(f"[伪原创8]   - {f}")
                                            else:
                                                log_func(f"[伪原创8]   ❌ 目录不存在: {output_dir}")
                                        except Exception as e:
                                            log_func(f"[伪原创8]   ❌ 无法列出目录内容: {str(e)}")
                        else:
                            if log_func:
                                log_func(f"[伪原创8] ❌ Spleeter执行失败: 返回码 {returncode}")
                                if stderr and len(stderr) > 0:
                                    log_func(f"[伪原创8] 错误信息: {stderr.strip()}")
                            # 当Spleeter返回错误时，设置标志变量
                            should_break_loop = True
                        
                        # 设置结果对象与原来的subprocess.run结果兼容
                        class SpleeterResult:
                            def __init__(self, returncode, stdout, stderr):
                                self.returncode = returncode
                                self.stdout = stdout
                                self.stderr = stderr
                                
                        spleeter_result = SpleeterResult(returncode, stdout, stderr)
                    
                    except subprocess.TimeoutExpired:
                        if log_func:
                            log_func(f"[伪原创8] ⚠️ 官方Spleeter执行超时，人声分离失败")
                        raise Exception("官方Spleeter执行超时")

                    if spleeter_result.returncode == 0:
                        # 获取最后一次分离的输出目录
                        final_spleeter_output_dir = spleeter_output_dirs[-1]
                        
                        # 对于多次分离，使用vocals_files列表中的最后一个文件
                        if vocal_sep_count > 1 and vocals_files:
                            # 直接使用最后一次分离的结果
                            if log_func:
                                log_func(f"[伪原创8] 多次分离完成，使用最终分离结果: {os.path.basename(vocals_files[-1])}")
                            return_vocals_file = vocals_files[-1]
                            
                            # 还需要处理伴奏文件 - 使用第一次分离的伴奏
                            first_input_name = os.path.splitext(os.path.basename(temp_audio))[0]
                            
                            # 获取第一次分离的伴奏文件
                            first_accompaniment = os.path.join(spleeter_output_dirs[0], first_input_name, "accompaniment.wav")
                            if os.path.exists(first_accompaniment):
                                # 验证文件大小是否合理
                                if os.path.getsize(first_accompaniment) > 50000:
                                    # 复制到标准位置
                                    shutil.copy2(first_accompaniment, temp_audio_accompaniment)
                                    shutil.copy2(return_vocals_file, temp_audio_vocals)
                                    
                                    # 如果到这里，就已经完成了人声分离，可以提前返回
                                    if log_func:
                                        if vocal_sep_count > 1:
                                            log_func(f"[伪原创8] ✅ 步骤2完成: AI人声分离成功（执行了{vocal_sep_count}次分离）")
                                        else:
                                            log_func(f"[伪原创8] ✅ 步骤2完成: AI人声分离成功")
                                        log_func(f"[伪原创8]   - 人声文件: {os.path.basename(temp_audio_vocals)} (大小: {os.path.getsize(return_vocals_file)/1024:.1f}KB)")
                                        log_func(f"[伪原创8]   - 原背景音乐: {os.path.basename(temp_audio_accompaniment)} (大小: {os.path.getsize(first_accompaniment)/1024:.1f}KB)")
                                    
                                    # 添加Spleeter输出目录到临时文件列表
                                    for dir_path in spleeter_output_dirs:
                                        add_temp_file(dir_path)
                                    if log_func:
                                        log_func(f"[伪原创8] 已记录Spleeter输出目录，将在处理完成后清理")
                                        
                                    # 使用分离后的人声文件作为输入
                                    vocals_input = temp_audio_vocals
                                    
                                    # 直接跳到下一步处理
                                    # 但不直接返回，让代码继续执行下面的逻辑以防万一
                                    # 只有在这些逻辑失败时才执行下面的备用逻辑
                                    
                        # 如果上面的逻辑不成功，执行默认逻辑
                        # 对于多次分离，获取最后一次分离的文件名
                        final_input_name = os.path.splitext(os.path.basename(current_input))[0]
                        
                        # 对于5stems模型，没有accompaniment.wav文件，需要合成伴奏
                        vocals_file = os.path.join(final_spleeter_output_dir, final_input_name, "vocals.wav")
                        drums_file = os.path.join(final_spleeter_output_dir, final_input_name, "drums.wav")
                        bass_file = os.path.join(final_spleeter_output_dir, final_input_name, "bass.wav")
                        piano_file = os.path.join(final_spleeter_output_dir, final_input_name, "piano.wav")
                        other_file = os.path.join(final_spleeter_output_dir, final_input_name, "other.wav")
                        
                        # 保存最后一次分离的vocals文件，用于后续处理
                        vocals_files.append(vocals_file)
                        
                        # 需要合成accompaniment文件（drums+bass+piano+other）
                        accompaniment_file = os.path.join(final_spleeter_output_dir, final_input_name, "accompaniment.wav")
                        
                        # 如果伴奏文件不存在，合成一个
                        if not os.path.exists(accompaniment_file) and all([os.path.exists(f) for f in [drums_file, bass_file, piano_file, other_file]]):
                            if log_func:
                                log_func("[伪原创8] 检测到5stems模型输出，正在合成伴奏文件...")
                            
                            # 使用ffmpeg合成伴奏文件
                            try:
                                merge_cmd = [
                                    "ffmpeg", "-y",
                                    "-i", drums_file,
                                    "-i", bass_file,
                                    "-i", piano_file,
                                    "-i", other_file,
                                    "-filter_complex", "[0:a][1:a][2:a][3:a]amix=inputs=4:normalize=0",
                                    "-ar", "44100",
                                    accompaniment_file
                                ]
                                
                                subprocess.run(merge_cmd, check=True, capture_output=True)
                                
                                if log_func:
                                    log_func("[伪原创8] 伴奏文件合成成功")
                            except Exception as e:
                                if log_func:
                                    log_func(f"[伪原创8] 伴奏文件合成失败: {e}")

                        # 定义更多可能的文件路径
                        possible_vocals_files = [
                            vocals_file,  # 标准路径
                            os.path.join(final_spleeter_output_dir, "input", "vocals.wav"),  # Demucs可能路径
                            os.path.join(final_spleeter_output_dir, "htdemucs", "input", "vocals.wav"),  # htdemucs模型路径
                            os.path.join(final_spleeter_output_dir, final_input_name, "vocals.wav"),  # 5stems模型路径
                        ]
                        
                        possible_accompaniment_files = [
                            accompaniment_file,  # 标准路径
                            os.path.join(final_spleeter_output_dir, "input", "accompaniment.wav"),  # Demucs可能路径
                            os.path.join(final_spleeter_output_dir, "input", "no_vocals.wav"),  # Demucs可能路径
                            os.path.join(final_spleeter_output_dir, "htdemucs", "input", "accompaniment.wav"),  # htdemucs模型路径
                            os.path.join(final_spleeter_output_dir, "htdemucs", "input", "no_vocals.wav"),  # htdemucs模型路径
                            os.path.join(final_spleeter_output_dir, final_input_name, "accompaniment.wav"),  # 自己合成的伴奏文件
                            # 5stems模型的各个轨道，作为备用
                            drums_file, 
                            bass_file,
                            piano_file,
                            other_file,
                        ]
                        
                        # 查找vocals文件
                        vocals_found = False
                        for v_file in possible_vocals_files:
                            if os.path.exists(v_file):
                                vocals_file = v_file
                                vocals_found = True
                                if log_func:
                                    log_func(f"[伪原创8] 找到人声文件: {vocals_file}")
                                break
                        
                        # 查找accompaniment文件
                        accompaniment_found = False
                        for a_file in possible_accompaniment_files:
                            if os.path.exists(a_file):
                                accompaniment_file = a_file
                                accompaniment_found = True
                                if log_func:
                                    log_func(f"[伪原创8] 找到伴奏文件: {accompaniment_file}")
                                break
                        
                        # 如果标准位置找不到，进行全局搜索
                        if not vocals_found or not accompaniment_found:
                            if log_func:
                                log_func(f"[伪原创8] 在标准位置未找到分离文件，开始全局搜索...")
                                
                            # 搜索整个输出目录
                            for root, dirs, files in os.walk(final_spleeter_output_dir):
                                for file in files:
                                    file_lower = file.lower()
                                    if not vocals_found and "vocals" in file_lower and file_lower.endswith(".wav"):
                                        vocals_file = os.path.join(root, file)
                                        vocals_found = True
                                        if log_func:
                                            log_func(f"[伪原创8] 全局搜索找到人声文件: {vocals_file}")
                                    elif not accompaniment_found and ("accompaniment" in file_lower or "no_vocals" in file_lower) and file_lower.endswith(".wav"):
                                        accompaniment_file = os.path.join(root, file)
                                        accompaniment_found = True
                                        if log_func:
                                            log_func(f"[伪原创8] 全局搜索找到伴奏文件: {accompaniment_file}")
                                    
                                    # 如果两个文件都找到，可以提前退出搜索
                                    if vocals_found and accompaniment_found:
                                        break
                                if vocals_found and accompaniment_found:
                                    break

                        if vocals_found and accompaniment_found and os.path.exists(vocals_file) and os.path.exists(accompaniment_file):
                            # 验证文件有效性
                            vocals_size = os.path.getsize(vocals_file)
                            accompaniment_size = os.path.getsize(accompaniment_file)
                            
                            # 检查文件大小是否合理(至少50KB)
                            if vocals_size > 50000 and accompaniment_size > 50000:
                                # 复制分离结果到标准位置
                                import shutil
                                shutil.copy2(vocals_file, temp_audio_vocals)
                                shutil.copy2(accompaniment_file, temp_audio_accompaniment)
                                
                                # 使用分离后的人声文件作为输入
                                vocals_input = temp_audio_vocals

                                if log_func:
                                    if vocal_sep_count > 1:
                                        log_func(f"[伪原创8] ✅ 步骤2完成: AI人声分离成功（执行了{vocal_sep_count}次分离）")
                                    else:
                                        log_func(f"[伪原创8] ✅ 步骤2完成: AI人声分离成功")
                                    log_func(f"[伪原创8]   - 人声文件: {os.path.basename(temp_audio_vocals)} (大小: {vocals_size/1024:.1f}KB)")
                                    log_func(f"[伪原创8]   - 原背景音乐: {os.path.basename(temp_audio_accompaniment)} (大小: {accompaniment_size/1024:.1f}KB)")
                                    
                                    if vocal_sep_count > 1:
                                        # 显示每次分离的输出目录
                                        for i, output_dir in enumerate(spleeter_output_dirs):
                                            log_func(f"[伪原创8]   - 第{i+1}次分离输出目录: {output_dir}")
                                
                                # 添加Spleeter输出目录到临时文件列表，最终清理时统一处理
                                # Add all Spleeter output directories to temporary files list
                                for dir_path in spleeter_output_dirs:
                                    add_temp_file(dir_path)
                                if log_func:
                                    log_func(f"[伪原创8] 已记录Spleeter输出目录，将在处理完成后清理")
                            else:
                                # 文件过小，可能分离失败
                                if log_func:
                                    log_func(f"[伪原创8] ⚠️ 警告: 分离结果文件过小，可能分离失败")
                                    log_func(f"[伪原创8]   - 人声文件大小: {vocals_size/1024:.1f}KB")
                                    log_func(f"[伪原创8]   - 伴奏文件大小: {accompaniment_size/1024:.1f}KB")
                                
                                # 直接抛出异常，不使用备用方案
                                raise Exception("人声分离结果文件过小，分离失败")
                        else:
                            if log_func:
                                log_func(f"[伪原创8] ⚠️ 未找到完整的分离结果文件")
                                log_func(f"[伪原创8]   - 人声文件找到: {vocals_found}")
                                log_func(f"[伪原创8]   - 伴奏文件找到: {accompaniment_found}")
                            
                            # 直接抛出异常，不使用备用方案
                            raise Exception("未找到完整的人声分离结果文件")
                    else:
                        if log_func:
                            log_func(f"[伪原创8] ⚠️ 官方Spleeter执行失败: {spleeter_result.stderr}")
                        raise Exception(f"官方Spleeter执行失败: {spleeter_result.returncode}")

                except subprocess.TimeoutExpired:
                    if log_func:
                        log_func(f"[伪原创8] ⚠️ 官方Spleeter执行超时，人声分离失败")
                    raise Exception("官方Spleeter执行超时")

                except Exception as e2:
                    if log_func:
                        log_func(f"[伪原创8] ⚠️ 官方Spleeter分离失败: {str(e2)}")
                        log_func(f"[伪原创8] 人声分离处理终止")
                    
                    # 确保vocals_files不为空，防止其他地方访问时出错
                    if not vocals_files and os.path.exists(temp_audio):
                        # 如果分离失败但源音频文件存在，添加一个假的vocals_file确保代码不崩溃
                        vocals_files.append(temp_audio)
                        if log_func:
                            log_func(f"[伪原创8] 添加原始音频作为备用，防止索引错误")
                    
                    raise Exception(f"官方Spleeter分离失败: {str(e2)}")
            else:
                if log_func:
                    log_func(f"[伪原创8] 未找到Python 3.8环境或官方版Spleeter，人声分离失败")
                raise Exception("未找到Python 3.8环境中的Spleeter")

            # 步骤3：人声响度平衡处理（专门针对人声）
            try:
                if log_func:
                    log_func(f"[伪原创8] 步骤3/7: 开始人声响度平衡处理...")

                # 导入必要的库
                import librosa
                import soundfile as sf
                import numpy as np

                # 确保人声文件存在
                if not os.path.exists(temp_audio_vocals):
                    if log_func:
                        log_func(f"[伪原创8] 人声文件不存在，人声分离可能失败")
                    # 直接抛出异常，不使用备用方案
                    raise Exception("人声文件不存在，人声分离可能失败")
                else:
                    # 读取人声文件
                    y_vocals, sr_vocals = librosa.load(temp_audio_vocals, sr=44100, mono=False)
                    if y_vocals.ndim > 1:
                        y_vocals = librosa.to_mono(y_vocals)

                    # 计算人声的整体RMS响度
                    current_rms = np.sqrt(np.mean(y_vocals**2))
                    if log_func:
                        log_func(f"[伪原创8] 人声当前整体RMS响度: {current_rms:.6f}")

                    # 设置目标响度（保守值，避免削波）
                    target_rms = 0.15  # 更保守的目标响度

                    if current_rms > 0:  # 避免除零错误
                        # 分段响度平衡处理
                        segment_duration = 5.0  # 每段5秒
                        segment_samples = int(segment_duration * sr_vocals)
                        num_segments = int(np.ceil(len(y_vocals) / segment_samples))

                        y_vocals_balanced = np.copy(y_vocals)

                        if log_func:
                            log_func(f"[伪原创8] 开始分段响度平衡，共{num_segments}段")

                        for i in range(num_segments):
                            start_idx = i * segment_samples
                            end_idx = min((i + 1) * segment_samples, len(y_vocals))

                            segment = y_vocals[start_idx:end_idx]
                            segment_rms = np.sqrt(np.mean(segment**2))

                            if segment_rms > 0.001:  # 避免静音段
                                # 计算该段的增益
                                segment_gain = target_rms / segment_rms

                                # 限制增益范围，避免过度放大
                                segment_gain = np.clip(segment_gain, 0.3, 2.0)

                                # 应用增益到该段
                                balanced_segment = segment * segment_gain

                                # 软限制器，避免削波
                                max_val = np.max(np.abs(balanced_segment))
                                if max_val > 0.95:
                                    balanced_segment = balanced_segment * (0.95 / max_val)

                                y_vocals_balanced[start_idx:end_idx] = balanced_segment

                        # 计算处理后的整体RMS响度
                        final_rms = np.sqrt(np.mean(y_vocals_balanced**2))
                        max_amplitude = np.max(np.abs(y_vocals_balanced))

                        if log_func:
                            log_func(f"[伪原创8] 分段响度平衡完成:")
                            log_func(f"[伪原创8]   - 原始响度: {current_rms:.6f}")
                            log_func(f"[伪原创8]   - 目标响度: {target_rms:.6f}")
                            log_func(f"[伪原创8]   - 最终响度: {final_rms:.6f}")
                            log_func(f"[伪原创8]   - 最大振幅: {max_amplitude:.6f}")
                            log_func(f"[伪原创8]   - 响度变化: {((final_rms/current_rms - 1) * 100):+.1f}%")
                            log_func(f"[伪原创8]   - 处理段数: {num_segments}")
                    else:
                        if log_func:
                            log_func(f"[伪原创8] 人声静音，跳过响度平衡")
                        y_vocals_balanced = y_vocals

                    # 保存响度平衡后的人声（步骤3）
                    sf.write(temp_audio_vocals_balanced, y_vocals_balanced, sr_vocals, subtype='PCM_16')

                    if log_func:
                        log_func(f"[伪原创8] ✅ 步骤3完成: 人声响度平衡文件已保存 -> {os.path.basename(temp_audio_vocals_balanced)}")

                    # 更新人声输入为响度平衡后的人声
                    vocals_input = temp_audio_vocals_balanced

            except Exception as e:
                if log_func:
                    log_func(f"[伪原创8] 人声响度平衡处理异常: {str(e)}")
                    log_func(f"[伪原创8] 人声响度平衡异常详情: {traceback.format_exc()}")
                # 响度平衡失败直接抛出异常
                raise Exception(f"人声响度平衡处理失败: {str(e)}")

            # 步骤4：将人声与新背景音乐混音
            if log_func:
                log_func(f"[伪原创8] 步骤4/7: 开始新背景音乐混音...")

            # 随机选择背景音乐
            bgm_folder = os.path.join(os.path.dirname(__file__), 'bgm')

            if os.path.exists(bgm_folder) and os.path.isdir(bgm_folder):
                bgm_files = [f for f in os.listdir(
                    bgm_folder) if f.endswith('.mp3')]
                if bgm_files:
                    bgm_file = os.path.join(
                        bgm_folder, random.choice(bgm_files))
                    if log_func:
                        log_func(
                            f"[伪原创8] 选择背景音乐: {os.path.basename(bgm_file)}")

                    # 将分离后的人声与新背景音乐混音
                    try:
                        # 随机生成背景音乐参数 - 音量大幅降低
                        original_volume = round(random.uniform(0.15, 0.25), 2)  # 原始音量：15%-25%
                        bgm_volume = round(original_volume * 0.2, 3)  # 先减小50%再降低60%: 0.5 * 0.4 = 0.2
                        bgm_delay = int(random.uniform(0, 2000))  # 背景音乐延迟：0-2秒
                        bgm_fade_in = int(random.uniform(1000, 3000))  # 淡入时间：1-3秒

                        # 使用高级ffmpeg混音 - 添加背景音乐循环
                        mix_cmd = [
                            'ffmpeg', '-y',
                            '-i', vocals_input,  # 使用分离后的人声（或原音频）
                            '-stream_loop', '-1', '-i', bgm_file,  # 新背景音乐（无限循环）
                            '-filter_complex',
                            # 复杂滤镜链 - 背景音乐循环播放
                            f'[0:a]aformat=sample_fmts=fltp:sample_rates=44100:channel_layouts=stereo[vocals];' +
                            f'[1:a]aformat=sample_fmts=fltp:sample_rates=44100:channel_layouts=stereo,' +
                            f'volume={bgm_volume},' +  # 降低背景音乐音量
                            f'adelay={bgm_delay}|{bgm_delay},' +  # 延迟背景音乐开始
                            f'afade=t=in:st=0:d={bgm_fade_in/1000}[bgm];' +  # 背景音乐淡入
                            f'[vocals][bgm]amix=inputs=2:duration=first:dropout_transition=2:normalize=1:weights=0.92 0.08[mixed];' +  # 保护人声主导混音
                            f'[mixed]pan=stereo|c0=0.9*c0+0.1*c1|c1=0.1*c0+0.9*c1[panned];' +  # 轻微立体声处理，保护音色
                            f'[panned]volume=1.2[final]',  # 补偿音量，去除有害延迟
                            '-map', '[final]',  # 使用最终处理的音频
                            temp_audio_with_bgm  # 输出混音
                        ]

                        if log_func:
                            log_func(f"[伪原创8] 背景音乐参数: 原始音量={original_volume:.3f}, 调整后音量={bgm_volume:.3f} (总计降低80%), 延迟={bgm_delay}ms, 淡入={bgm_fade_in}ms")
                            log_func(f"[伪原创8] 背景音乐设置: 循环播放以匹配人声长度")
                            log_func(f"[伪原创8] 立体声处理: 轻微分离(90%/10%混合) + 音量补偿，保护人声音色")
                            log_func(f"[伪原创8] 执行人声+新背景音乐混音命令: {' '.join(mix_cmd)}")

                        process_mix = subprocess.run(mix_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

                        if process_mix.returncode != 0:
                            if log_func:
                                log_func(f"[伪原创8] 人声+背景音乐混音失败: {process_mix.stderr}")
                            # 混音失败直接抛出异常
                            raise Exception(f"人声+背景音乐混音失败: {process_mix.stderr}")
                        else:
                            if log_func:
                                log_func(f"[伪原创8] ✅ 步骤4完成: 人声+背景音乐混音成功 -> {os.path.basename(temp_audio_with_bgm)}")
                    except Exception as e:
                        if log_func:
                            log_func(f"[伪原创8] 人声+背景音乐混音处理异常: {str(e)}")
                        # 混音异常直接抛出
                        raise Exception(f"人声+背景音乐混音处理异常: {str(e)}")
                else:
                    if log_func:
                        log_func("[伪原创8] 没有找到背景音乐文件")
                    # 没有背景音乐文件直接报错
                    raise Exception("没有找到背景音乐文件，无法执行背景音乐混音")
            else:
                if log_func:
                    log_func("[伪原创8] 背景音乐文件夹不存在")
                # 背景音乐文件夹不存在直接报错
                raise Exception("背景音乐文件夹不存在，无法执行背景音乐混音")

            # 步骤5：对混音后的音频进行处理（启用轻微音高调整）
            if log_func:
                log_func(f"[伪原创8] 步骤5/7: 开始音频处理（启用轻微音高调整）...")

            # 应用音频处理 - 最小化处理以避免回音
            speed = 1.0  # 禁用变速处理
            # 启用音高调整，范围为-0.4到+0.4之间（确保使用优化的librosa方法）
            import random
            pitch_shift = round(random.uniform(-0.4, 0.4), 2)  # 随机音高调整，中等范围仍能保持良好音质
            noise_level = 0.002  # 降低噪声强度以提高音质
            
            if log_func:
                log_func(f"[伪原创8] 音高调整已启用，调整值: {pitch_shift} 半音")

            try:
                # 检查输入文件是否存在
                if not os.path.exists(temp_audio_with_bgm):
                    raise FileNotFoundError(f"输入音频文件不存在: {temp_audio_with_bgm}")

                # 使用librosa和soundfile处理音频
                import librosa
                import soundfile as sf
                import numpy as np

                # 保存步骤5的输出（新背景混音）
                step5_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                          f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_5_新背景混音_{sequence_number:03d}.wav")
                shutil.copy2(temp_audio_with_bgm, step5_output)
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤5输出已保存: {os.path.basename(step5_output)}")

                if log_func:
                    log_func(f"[伪原创8] 开始加载音频文件: {os.path.basename(temp_audio_with_bgm)}")

                # 读取混音后的音频，限制采样率避免内存问题
                y, sr = librosa.load(temp_audio_with_bgm, sr=44100, mono=False)

                # 步骤6.1: 保存原始读取的音频（测试librosa读取是否引入回声）
                step6_1_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                            f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_6_1_原始读取_{sequence_number:03d}.wav")

                if y.ndim > 1:
                    y_output_6_1 = y.T
                else:
                    y_output_6_1 = y

                sf.write(step6_1_output, y_output_6_1, sr, subtype='PCM_16')
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤6.1输出已保存: {os.path.basename(step6_1_output)} (测试librosa读取)")

                # 保留立体声信息，不强制转换为单声道，以保持人声平衡效果
                if y.ndim > 1:
                    # 如果是立体声，保持立体声格式进行处理
                    if log_func:
                        log_func(f"[伪原创8] 检测到立体声音频，保持立体声格式以保留人声平衡效果")
                        log_func(f"[伪原创8] 立体声音频形状: {y.shape} (声道数: {y.shape[0]}, 样本数: {y.shape[1]})")
                    # 对每个声道分别处理
                    process_stereo = True
                else:
                    process_stereo = False
                    y_mono = y
                    if log_func:
                        log_func(f"[伪原创8] 单声道音频，样本数: {len(y)}")

                # 正确计算音频时长（处理立体声情况）
                if y.ndim > 1:
                    audio_duration = y.shape[1] / sr  # 立体声：shape[1]是样本数
                else:
                    audio_duration = len(y) / sr      # 单声道：len(y)是样本数

                if log_func:
                    log_func(f"[伪原创8] 音频加载成功，采样率: {sr}Hz, 时长: {audio_duration:.2f}秒")

                # 1. 变速处理 (不影响音高) - 跳过以避免回音
                if log_func:
                    log_func(f"[伪原创8] 开始变速处理，倍数: {speed}")

                if speed != 1.0:
                    # 只有在真正需要变速时才进行处理
                    if process_stereo:
                        # 对立体声的每个声道分别处理，保持人声平衡
                        y_speed_channels = []
                        for channel in range(y.shape[0]):
                            channel_processed = librosa.effects.time_stretch(y[channel], rate=speed)
                            y_speed_channels.append(channel_processed)
                        # 重新组合为立体声数组
                        y_speed = np.array(y_speed_channels)
                    else:
                        y_speed = librosa.effects.time_stretch(y, rate=speed)
                else:
                    # speed=1.0时直接跳过，避免librosa引入的回音问题
                    if log_func:
                        log_func(f"[伪原创8] 跳过变速处理（speed=1.0，避免回音问题）")
                    y_speed = y

                # 步骤6.2: 保存变速处理结果
                step6_2_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                            f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_6_2_变速处理_{sequence_number:03d}.wav")

                if process_stereo:
                    y_output_6_2 = y_speed.T
                else:
                    y_output_6_2 = y_speed

                sf.write(step6_2_output, y_output_6_2, sr, subtype='PCM_16')
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤6.2输出已保存: {os.path.basename(step6_2_output)} (测试变速处理)")

                # 2. 音高调整 - 当pitch_shift不为0时才进行音高调整
                    if log_func:
                        if pitch_shift == 0:
                            log_func(f"[伪原创8] 跳过音高调整，pitch_shift={pitch_shift} (无调整)")
                        else:
                            log_func(f"[伪原创8] 开始音高调整，半音: {pitch_shift}")

                    # 当pitch_shift为0时跳过音高调整处理，直接使用变速处理的结果
                    if pitch_shift == 0:
                        y_shifted = y_speed  # 直接使用变速处理的结果，不进行音高调整
                        if log_func:
                            log_func(f"[伪原创8] 已禁用音高调整，保持原始音调")
                    else:
                        if process_stereo:
                            # 真正的立体声处理：分别处理每个声道，保持独立性
                            if log_func:
                                log_func(f"[伪原创8] 对立体声的每个声道独立进行音高调整")

                            y_shifted_channels = []
                            for channel in range(y_speed.shape[0]):
                                if log_func:
                                    log_func(f"[伪原创8] 处理声道 {channel + 1}/{y_speed.shape[0]}")

                                # 对每个声道独立进行无损音高调整
                                channel_shifted = no_loss_pitch_shift(
                                    y_speed[channel],
                                    sr,
                                    pitch_shift,
                                    log_func
                                )
                                y_shifted_channels.append(channel_shifted)

                            # 重新组合为立体声数组
                            y_shifted = np.array(y_shifted_channels)

                            if log_func:
                                log_func(f"[伪原创8] 立体声音高调整完成，保持声道独立性")
                        else:
                            # 单声道处理
                            y_shifted = no_loss_pitch_shift(y_speed, sr, pitch_shift, log_func)
                            if log_func:
                                log_func(f"[伪原创8] 单声道音高调整完成")

                # 步骤6.3: 保存音高调整结果
                step6_3_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                            f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_6_3_音高调整_{sequence_number:03d}.wav")

                if process_stereo:
                    y_output_6_3 = y_shifted.T
                else:
                    y_output_6_3 = y_shifted

                sf.write(step6_3_output, y_output_6_3, sr, subtype='PCM_16')
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤6.3输出已保存: {os.path.basename(step6_3_output)} (测试音高调整)")

                # 3. 添加白噪声 - 极低强度处理
                if noise_level > 0:
                    if log_func:
                        log_func(f"[伪原创8] 添加极轻微白噪声，强度: {noise_level}")

                    if process_stereo:
                        noise = np.random.normal(0, noise_level, y_shifted.shape).astype(np.float32)
                        y_noisy = y_shifted + noise
                    else:
                        noise = np.random.normal(0, noise_level, len(y_shifted)).astype(np.float32)
                        y_noisy = y_shifted + noise
                else:
                    if log_func:
                        log_func(f"[伪原创8] 跳过噪声添加（noise_level={noise_level}）")
                    y_noisy = y_shifted

                # 步骤6.4: 保存噪声添加结果
                step6_4_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                            f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_6_4_添加噪声_{sequence_number:03d}.wav")

                if process_stereo:
                    y_output_6_4 = y_noisy.T
                else:
                    y_output_6_4 = y_noisy

                sf.write(step6_4_output, y_output_6_4, sr, subtype='PCM_16')
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤6.4输出已保存: {os.path.basename(step6_4_output)} (测试噪声添加)")

                # 严格的音量控制，避免过曝
                # 1. 首先检查峰值
                peak_value = np.max(np.abs(y_noisy))
                if peak_value > 0.95:  # 如果峰值过高，进行缩放
                    scale_factor = 0.9 / peak_value  # 缩放到90%以留出安全余量
                    y_noisy = y_noisy * scale_factor
                    if log_func:
                        log_func(f"[伪原创8] 检测到音频峰值过高({peak_value:.3f})，应用缩放系数: {scale_factor:.3f}")

                # 2. 再次确保在安全范围内
                y_noisy = np.clip(y_noisy, -0.95, 0.95)  # 使用更保守的范围

                # 步骤6.5: 保存音量控制结果
                step6_5_output = os.path.join(os.path.dirname(temp_audio_with_bgm),
                                            f"{os.path.splitext(os.path.basename(output_path))[0]}.step8_6_5_音量控制_{sequence_number:03d}.wav")

                if process_stereo:
                    y_output_6_5 = y_noisy.T
                else:
                    y_output_6_5 = y_noisy

                sf.write(step6_5_output, y_output_6_5, sr, subtype='PCM_16')
                if log_func:
                    log_func(f"[伪原创8] 💾 步骤6.5输出已保存: {os.path.basename(step6_5_output)} (测试音量控制)")

                # 3. 最终音频检查
                final_rms = np.sqrt(np.mean(y_noisy**2))
                final_peak = np.max(np.abs(y_noisy))
                if log_func:
                    log_func(f"[伪原创8] 最终音频检查: RMS={final_rms:.6f}, 峰值={final_peak:.6f}")

                if log_func:
                    log_func(f"[伪原创8] 开始保存处理后的音频: {os.path.basename(temp_audio_processed)}")

                # 保存处理后的音频，保持原有的声道格式
                if process_stereo:
                    # 立体声格式：转置数组以符合soundfile的格式要求 (samples, channels)
                    y_output = y_noisy.T
                    if log_func:
                        log_func(f"[伪原创8] 保存立体声音频，形状: {y_output.shape}")
                else:
                    y_output = y_noisy
                    if log_func:
                        log_func(f"[伪原创8] 保存单声道音频，长度: {len(y_output)}")

                sf.write(temp_audio_processed, y_output, sr, subtype='PCM_16')

                if log_func:
                    log_func(f"[伪原创8] ✅ 步骤6完成: 音频处理文件已保存 -> {os.path.basename(temp_audio_processed)}")
                    log_func(f"[伪原创8]   - 已生成详细子步骤文件用于回声问题诊断:")
                    log_func(f"[伪原创8]   - 6.1: 原始读取 (测试librosa读取)")
                    log_func(f"[伪原创8]   - 6.2: 变速处理 (测试time_stretch)")
                    log_func(f"[伪原创8]   - 6.3: 音高调整 (测试pitch_shift)")
                    log_func(f"[伪原创8]   - 6.4: 添加噪声 (测试噪声影响)")
                    log_func(f"[伪原创8]   - 6.5: 音量控制 (测试音量处理)")

            except Exception as e:
                if log_func:
                    log_func(f"[伪原创8] 音频处理异常: {str(e)}")
                    log_func(f"[伪原创8] 异常详情: {traceback.format_exc()}")
                # 音频处理失败直接抛出异常
                raise Exception(f"音频处理失败: {str(e)}")

            # 步骤7：最终混音（直接复制步骤6的结果）
            try:
                if log_func:
                    log_func(f"[伪原创8] 步骤7/7: 生成最终混音文件...")

                # 直接复制步骤6的音频处理结果作为最终混音
                import shutil
                shutil.copy2(temp_audio_processed, temp_audio_mixed)

                if log_func:
                    log_func(f"[伪原创8] ✅ 步骤7完成: 最终混音文件已生成 -> {os.path.basename(temp_audio_mixed)}")

            except Exception as e:
                if log_func:
                    log_func(f"[伪原创8] 最终混音处理异常: {str(e)}")
                # 最终混音失败直接抛出异常
                raise Exception(f"最终混音处理失败: {str(e)}")

            # 最终处理完成日志
            if log_func:
                log_func(f"[伪原创8] ✅ 音频处理完成（7个步骤）")
                log_func(f"[伪原创8]   - 步骤1: 原始音频提取")
                log_func(f"[伪原创8]   - 步骤2: AI人声分离")
                log_func(f"[伪原创8]   - 步骤3: 人声响度平衡 (目标RMS=0.8，专门针对人声)")
                log_func(f"[伪原创8]   - 步骤4: 新背景音乐混音")
                log_func(f"[伪原创8]   - 步骤6: 音频信号处理 (变速={speed}, 音高={pitch_shift}, 噪声={noise_level}, 真正立体声处理)")
                log_func(f"[伪原创8]   - 步骤6: 音频处理完成")
                log_func(f"[伪原创8]   - 步骤7: 最终混音输出")
                log_func(f"[伪原创8]   - 人声响度平衡: {os.path.basename(temp_audio_vocals_balanced)}")
                log_func(f"[伪原创8]   - 最终混音: {os.path.basename(temp_audio_mixed)}")

                # 添加更详细的处理信息
                try:
                    # 分析最终混音文件的响度
                    y_final, sr_final = librosa.load(temp_audio_mixed, sr=None, mono=True)
                    final_rms = np.sqrt(np.mean(y_final**2))
                    log_func(f"[伪原创8] 最终混音响度分析:")
                    log_func(f"[伪原创8]   - 最终RMS响度: {final_rms:.6f}")
                    log_func(f"[伪原创8]   - 最大振幅: {np.max(np.abs(y_final)):.6f}")
                    log_func(f"[伪原创8]   - 声道数: {'立体声' if process_stereo else '单声道'}")
                    log_func(f"[伪原创8]   - 采样率: {sr_final}Hz")
                except Exception as ex:
                    log_func(f"[伪原创8] 最终混音响度分析失败: {str(ex)}")

            # 步骤8：合并音频和视频
            merge_cmd = [
                'ffmpeg', '-y',
                '-i', audio_input_file,  # 视频输入
                '-i', temp_audio_mixed,  # 混音后的音频
                '-c:v', 'copy',  # 复制视频流
                '-c:a', 'aac',  # 使用AAC编码音频
                '-b:a', '192k',  # 音频比特率
                '-map', '0:v:0',  # 使用第一个输入的视频流
                '-map', '1:a:0',  # 使用第二个输入的音频流
                temp_step8  # 输出到第8步文件
            ]

            if log_func:
                log_func(f"[伪原创8] 步骤8/8: 开始音视频合并...")
                log_func(f"[伪原创8] 执行音视频合并命令: {' '.join(merge_cmd)}")

            if log_func:
                log_func(f"[伪原创8] 执行音视频合并命令: {' '.join(merge_cmd)}")

            process_merge = subprocess.run(merge_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

            if process_merge.returncode != 0:
                if log_func:
                    log_func(f"[伪原创8] 音视频合并失败: {process_merge.stderr}")
                    log_func("[伪原创8] 将使用原始视频继续处理")
                # 合并失败，终止处理
                error_details = "音视频合并失败"
                if log_func:
                    log_func(f"[伪原创8] 错误：{error_details}，处理中止")

                # 更新失败状态
                update_feature_status("伪原创8-音频混音处理", "失败", error_details)

                # 正式完成第八步，使用状态管理器（失败状态）
                status_manager.complete_step("音频混音处理", 8, TOTAL_STEPS, success=False, details=error_details, output_file=None)

                # 清理临时文件并终止处理
                result["success"] = False
                result["error"] = error_details
                return result
            else:
                if log_func:
                    log_func(f"[伪原创8] ✅ 步骤7完成: 音视频合并成功 -> {os.path.basename(temp_step8)}")
                audio_output = temp_step8

        if log_func:
            log_func("[伪原创8] 进度: 100%")
            log_func(f"[伪原创8] ✅ 步骤8/8: 音频混音处理全部完成")
            log_func(f"[伪原创8] 最终输出文件: {os.path.basename(audio_output)}")

        # 构建成功状态信息
        try:
            # 尝试获取背景音乐参数（如果定义了）
            bgm_info = f"背景音量={bgm_volume:.2f}, 延迟={bgm_delay}ms, 淡入={bgm_fade_in}ms"
        except NameError:
            # 如果背景音乐参数未定义（例如跳过了混音）
            bgm_info = "无背景音乐混音"

        success_details = f"变速倍数={speed}, 音高微调={pitch_shift:.2f}, 白噪声强度={noise_level}, {bgm_info}"
        update_feature_status("伪原创8-音频混音处理", "成功", success_details)

        # 正式完成第八步，使用状态管理器
        status_manager.complete_step("音频混音处理", 8, 11, success=True, details=success_details, output_file=audio_output)

        if log_func:
            log_func(f"[伪原创] Step 8/11 完成：{success_details}")

        # ========== Step 9: 变速处理 ==========
        # 变速功能已启用，随机选择1.03到1.05的变速倍数

        # 确定第9步的输入文件（第8步的输出）
        if not os.path.exists(temp_step8):
            error_details = "第8步输出文件不存在，无法进行变速处理"
            if log_func:
                log_func(f"[伪原创9] 错误: {error_details}")

            result["success"] = False
            result["error"] = error_details
            status_manager.complete_step("变速处理", 9, 11, success=False, details=error_details)
            return result

        # 生成第9步输出文件路径
        temp_step9 = os.path.join(os.path.dirname(temp_step8),
                                 f"{os.path.splitext(os.path.basename(output_path))[0]}.step9.mp4")

        # 开始第9步变速处理
        status_manager.start_step("变速处理", 9, 11, temp_step8)
        status_manager.log_progress(9, 0)

        if log_func:
            log_func("[伪原创] Step 9/11: 变速处理开始...")
            log_func(f"[伪原创9] 使用第8步音频混音后的文件作为输入: {temp_step8}")

        try:
            # 随机选择变速倍数（1.03到1.05）
            speed_factor = round(random.uniform(1.03, 1.05), 3)

            if log_func:
                log_func(f"[伪原创9] 随机选择变速倍数: {speed_factor}x")

            # 应用变速处理
            success = apply_speed_change(temp_step8, temp_step9, speed_factor, log_func)

            if success:
                success_details = f"变速处理完成，倍数: {speed_factor}x"
                if log_func:
                    log_func(f"[伪原创9] ✅ {success_details}")

                status_manager.complete_step("变速处理", 9, 11, success=True, details=success_details, output_file=temp_step9)

                if log_func:
                    log_func(f"[伪原创] Step 9/11 完成：{success_details}")
            else:
                error_details = "变速处理失败"
                if log_func:
                    log_func(f"[伪原创9] ❌ {error_details}")

                result["success"] = False
                result["error"] = error_details
                status_manager.complete_step("变速处理", 9, 11, success=False, details=error_details)
                return result

        except Exception as e:
            error_details = f"变速处理异常: {str(e)}"
            if log_func:
                log_func(f"[伪原创9] ❌ {error_details}")

            result["success"] = False
            result["error"] = error_details
            status_manager.complete_step("变速处理", 9, 11, success=False, details=error_details)
            return result

        # ========== Step 10: 编码转换（H.265/HEVC） ==========
        # 检查是否启用H.265转换
        try:
            import config_manager
            config = config_manager.get_config()
            enable_h265 = config.get('enable_h265', False)
        except Exception as e:
            if log_func:
                log_func(f"[伪原创10] 读取H.265配置失败: {str(e)}")
            enable_h265 = False  # 默认禁用H.265

        # 初始化第10步输出变量（避免作用域问题）
        temp_step10 = None
        step10_output = None

        if not enable_h265:
            # 如果未启用H.265转换，跳过第10步，直接进入第11步
            if log_func:
                log_func("[伪原创] Step 10/11: H.265转换未启用，跳过编码转换步骤")

            # 跳过第10步时，必须使用第9步的输出
            if not os.path.exists(temp_step9):
                error_details = "第9步输出文件不存在，无法跳过第10步"
                if log_func:
                    log_func(f"[伪原创10] 错误：{error_details}")

                # 更新功能状态
                update_feature_status("伪原创10-编码转换", "失败", error_details)

                # 清理临时文件并终止处理
                result["success"] = False
                result["error"] = error_details
                return result

            step10_output = temp_step9

            # 更新状态为跳过
            update_feature_status("伪原创10-编码转换", "跳过", "H.265转换未启用")
            status_manager.complete_step("编码转换", 10, 11, success=True,
                                       details="跳过H.265转换（未启用）", output_file=step10_output)

            if log_func:
                log_func(f"[伪原创] Step 10/11 跳过：H.265转换未启用，使用文件: {step10_output}")
        else:
            # 启用H.265转换，执行正常的编码转换流程
            if log_func:
                log_func("[伪原创] Step 10/11: H.265转换已启用，开始编码转换...")

        if enable_h265:
            # 第10步启用H.265时，必须使用第9步的输出
            if not os.path.exists(temp_step9):
                error_details = "第9步输出文件不存在，无法进行H.265编码转换"
                if log_func:
                    log_func(f"[伪原创10] 错误: {error_details}")
                update_feature_status("伪原创10-编码转换", "失败", error_details)
                result["success"] = False
                result["error"] = error_details
                return result

            encode_input_check = temp_step9

            streams_check = get_stream_types(encode_input_check)
            has_video_check = 'video' in streams_check

            if not has_video_check:
                if log_func:
                    log_func(f"[伪原创] 错误: 第9步输出文件 {encode_input_check} 不包含视频流！处理中止。")
                    log_func(f"[伪原创] 检测到流: {', '.join(streams_check)}")
                    log_func("[伪原创] 伪原创处理已终止，请确保处理链中的所有步骤都保留视频流。")

                # 更新功能状态
                update_feature_status("伪原创10-编码转换", "失败", "输入文件不包含视频流，处理中止")

                # 清理临时文件并终止处理
                result["success"] = False
                result["error"] = "输入文件不包含视频流，处理中止"
                return result

            # 第10步必须使用第9步的输出
            encode_input = temp_step9

            # 正式开始第十步，使用状态管理器
            status_manager.start_step("编码转换", 10, 11, encode_input)
            status_manager.log_progress(10, 0)

            if log_func:
                log_func("[伪原创] Step 10/11: 编码转换（H.265/HEVC）开始...")
                log_func(f"[伪原创10] 使用第9步变速处理后的文件作为输入: {encode_input}")

            # 获取输入文件信息
            try:
                file_info = status_manager.get_media_info(encode_input)
                if file_info and file_info.get("is_valid") and log_func:
                    log_func(f"[伪原创10] 输入文件信息:")
                    log_func(f"[伪原创10]   - 尺寸: {file_info.get('resolution', 'unknown')}")
                    log_func(f"[伪原创10]   - 时长: {file_info.get('duration', 'unknown')}秒")
                    log_func(f"[伪原创10]   - 编码: {file_info.get('video_codec', 'unknown')}")
            except Exception as e:
                if log_func:
                    log_func(f"[伪原创10] 获取输入文件信息失败: {str(e)}")

            # 设置标准化的输出路径
            temp_step10 = temp_base + ".step10.mp4"
            # 添加到临时文件跟踪列表
            add_temp_file(temp_step10)
            global_temp_files.append(temp_step10)

            # 验证步骤转换
            if not verify_step_transition(encode_input, temp_step10, 9, 10, "编码转换", result):
                # 验证失败，返回结果
                return result

            # 应用编码转换
            encode_success = apply_codec_conversion(encode_input, temp_step10, log_func)

            if encode_success and os.path.exists(temp_step10):
                if log_func:
                    log_func(f"[伪原创10] 编码转换成功，输出: {temp_step10}")

                # 删除将结果复制到最终输出路径的代码，让第11步完成这个工作

                # 更新功能状态（使用状态管理器的complete_step而不是update_feature_status）
                status_manager.complete_step("编码转换", 10, 11, success=True,
                                           details="H.265编码转换成功",
                                           output_file=temp_step10)
            else:
                if log_func:
                    log_func(f"[伪原创10] 编码转换失败")

                # 更新失败状态
                status_manager.complete_step("编码转换", 10, 11, success=False,
                                           details="编码转换失败",
                                           output_file=None)

                # 编码转换失败，终止处理
                error_details = "编码转换失败"
                if log_func:
                    log_func(f"[伪原创10] 错误：{error_details}，处理中止")

                # 清理临时文件并终止处理
                result["success"] = False
                result["error"] = error_details
                return result

        # 执行第11步：MP4头部修复
        # 确定第11步的输入文件
        if enable_h265 and temp_step10 is not None and os.path.exists(temp_step10):
            # 如果启用了H.265转换且第10步成功，使用第10步的输出
            step11_input = temp_step10
        else:
            # 如果未启用H.265转换，使用第9步或更早步骤的输出
            if step10_output is not None and os.path.exists(step10_output):
                step11_input = step10_output
            elif 'audio_output' in locals() and os.path.exists(audio_output):
                step11_input = audio_output
            elif os.path.exists(temp_step9):
                step11_input = temp_step9
            elif os.path.exists(temp_step8):
                step11_input = temp_step8
            elif os.path.exists(temp_step7):
                step11_input = temp_step7
            elif os.path.exists(temp_step6):
                step11_input = temp_step6
            else:
                step11_input = output_path

        # 设置标准化的第11步输出路径
        temp_step11 = temp_base + ".step11.mp4"
        # 添加到临时文件跟踪列表
        add_temp_file(temp_step11)
        global_temp_files.append(temp_step11)

        status_manager.log_detail("[伪原创] 开始执行第11步：MP4头部修复")
        if log_func:
            log_func(f"[伪原创] Step 11/11: MP4头部修复开始...")
            log_func(f"[伪原创11] 使用输入文件: {step11_input}")

        # 获取输入文件信息
        try:
            file_info = status_manager.get_media_info(step11_input)
            if file_info and file_info.get("is_valid") and log_func:
                log_func(f"[伪原创11] 输入文件信息:")
                log_func(f"[伪原创11]   - 尺寸: {file_info.get('resolution', 'unknown')}")
                log_func(f"[伪原创11]   - 时长: {file_info.get('duration', 'unknown')}秒")
                log_func(f"[伪原创11]   - 编码: {file_info.get('video_codec', 'unknown')}")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创11] 获取输入文件信息失败: {str(e)}")

        # 使用确定的输入文件进行MP4头部修复
        step11_success = apply_mp4_header_fix(step11_input, temp_step11, log_func)

        # 如果处理成功，将结果复制到最终输出路径
        if step11_success and os.path.exists(temp_step11):
            # 更新第11步状态为成功
            status_manager.update_status("伪原创11-MP4头部修复", StepStatus.SUCCESS, "MP4头部修复成功")

            # 复制文件到最终输出路径，并重命名为"视频.mp4"
            try:
                # 获取输出路径的目录
                output_dir = os.path.dirname(output_path)
                # 创建新的输出路径，使用"视频.mp4"作为文件名
                new_output_path = os.path.join(output_dir, "视频.mp4")
                
                # 使用copy2而不是copy以保留文件元数据
                shutil.copy2(temp_step11, new_output_path)
                status_manager.log_detail(f"[伪原创] 已将第11步结果复制到最终输出路径: {new_output_path}")
                
                # 更新输出路径变量，确保后续代码使用新的路径
                output_path = new_output_path
                
                # 验证复制后的文件是否存在且大小一致
                if os.path.exists(output_path):
                    src_size = os.path.getsize(temp_step11)
                    dst_size = os.path.getsize(output_path)
                    if src_size == dst_size:
                        status_manager.log_detail(f"[伪原创] 文件复制验证成功: {src_size} bytes")
                    else:
                        status_manager.log_detail(f"[伪原创] 警告：复制后文件大小不一致 - 源:{src_size} bytes, 目标:{dst_size} bytes")
                else:
                    status_manager.log_detail(f"[伪原创] 警告：复制后目标文件不存在: {output_path}")
            except Exception as copy_err:
                status_manager.log_detail(f"[伪原创] 复制到最终输出路径失败: {copy_err}")

                # 尝试备用复制方法
                try:
                    status_manager.log_detail(f"[伪原创] 尝试备用复制方法...")
                    # 获取输出路径的目录
                    output_dir = os.path.dirname(output_path)
                    # 创建新的输出路径，使用"视频.mp4"作为文件名
                    new_output_path = os.path.join(output_dir, "视频.mp4")
                    
                    with open(temp_step11, 'rb') as src_file:
                        with open(new_output_path, 'wb') as dst_file:
                            dst_file.write(src_file.read())
                    
                    # 更新输出路径变量，确保后续代码使用新的路径
                    output_path = new_output_path
                    status_manager.log_detail(f"[伪原创] 备用复制方法成功")
                except Exception as alt_copy_err:
                    status_manager.log_detail(f"[伪原创] 备用复制也失败: {alt_copy_err}")
        elif os.path.exists(temp_step11):
            # 第11步失败但文件存在，仍尝试复制
            try:
                # 获取输出路径的目录
                output_dir = os.path.dirname(output_path)
                # 创建新的输出路径，使用"视频.mp4"作为文件名
                new_output_path = os.path.join(output_dir, "视频.mp4")
                
                shutil.copy2(temp_step11, new_output_path)
                # 更新输出路径变量，确保后续代码使用新的路径
                output_path = new_output_path
                status_manager.log_detail(f"[伪原创] 第11步失败但文件存在，已复制到最终输出路径: {output_path}")
            except Exception as copy_err:
                status_manager.log_detail(f"[伪原创] 复制失败文件到最终输出路径失败: {copy_err}")
                
                # 注意：临时文件清理现在在finally块中统一处理，这里不再单独清理
                # 确保文件复制成功后，标记可以清理
                status_manager.log_detail(f"[伪原创] 第11步处理完成，文件已复制到: {output_path}")
        elif not step11_success:
            # 第11步处理失败，更新状态
            status_manager.update_status("伪原创11-MP4头部修复", StepStatus.FAILURE, "MP4头部修复失败")
        else:
            status_manager.log_detail("[伪原创] 跳过第11步：找不到第10步的输出文件")
            # 跳过第11步，更新状态为跳过
            status_manager.update_status("伪原创11-MP4头部修复", StepStatus.SKIPPED, "跳过：找不到第10步的输出文件")

        # 添加封面生成逻辑
        try:
            # 获取封面信息
            output_dir = os.path.dirname(output_path)
            
            # 记录封面模式信息
            is_generate_mode = cover_mode == 'generate'
            is_copy_mode = cover_mode == 'copy'
            
            if log_func:
                log_func(f"[伪原创] 当前封面模式: {cover_mode}")
            
            # 检查是否提供了封面源文件
            if not cover_landscape and not cover_portrait:
                if log_func:
                    log_func("[伪原创] 错误: 未提供封面源文件，无法生成封面")
                    log_func("[伪原创] 提示: 请确保在待发布列表中添加了封面图片，或者在视频所在文件夹中放置名为'横屏封面.png'或'竖屏封面.png'的图片")
            else:
                # 记录封面源文件信息
                if cover_landscape:
                    log_func(f"[伪原创] 使用横屏封面源文件: {cover_landscape}")
                    # 添加详细日志
                    log_func(f"[伪原创] 【诊断】横屏封面文件是否存在: {os.path.exists(cover_landscape)}")
                    if os.path.exists(cover_landscape):
                        log_func(f"[伪原创] 【诊断】横屏封面文件大小: {os.path.getsize(cover_landscape)} 字节")
                        log_func(f"[伪原创] 【诊断】横屏封面文件是否可读: {os.access(cover_landscape, os.R_OK)}")
                    else:
                        log_func(f"[伪原创] 【诊断】尝试列出目录内容: {os.path.dirname(cover_landscape)}")
                        try:
                            dir_files = os.listdir(os.path.dirname(cover_landscape))
                            log_func(f"[伪原创] 【诊断】目录中的文件: {dir_files[:5]}...")
                        except Exception as dir_err:
                            log_func(f"[伪原创] 【诊断】列出目录失败: {dir_err}")
                    
                    if not os.path.exists(cover_landscape):
                        log_func(f"[伪原创] 警告: 横屏封面源文件不存在: {cover_landscape}")
                if cover_portrait:
                    log_func(f"[伪原创] 使用竖屏封面源文件: {cover_portrait}")
                    # 添加详细日志
                    log_func(f"[伪原创] 【诊断】竖屏封面文件是否存在: {os.path.exists(cover_portrait)}")
                    if os.path.exists(cover_portrait):
                        log_func(f"[伪原创] 【诊断】竖屏封面文件大小: {os.path.getsize(cover_portrait)} 字节")
                        log_func(f"[伪原创] 【诊断】竖屏封面文件是否可读: {os.access(cover_portrait, os.R_OK)}")
                    
                    if not os.path.exists(cover_portrait):
                        log_func(f"[伪原创] 警告: 竖屏封面源文件不存在: {cover_portrait}")
            
            # 如果需要生成或复制封面，且有封面源文件
            if (is_generate_mode or is_copy_mode) and (cover_landscape or cover_portrait):
                font_path = os.path.join(os.path.dirname(__file__), "fonts", "SourceHanSansSC-Medium.otf")
                cover_success = False
                
                # 决定封面输出路径
                if is_copy_mode:  # 使用is_copy_mode而不是copy_cover
                    landscape_cover_path = os.path.join(output_dir, "横屏封面.png")
                    portrait_cover_path = os.path.join(output_dir, "竖屏封面.png")
                else:
                    landscape_cover_path = os.path.join(output_dir, "横屏封面1.png")
                    portrait_cover_path = os.path.join(output_dir, "竖屏封面1.png")
                
                # 处理横屏封面
                if cover_landscape and os.path.exists(cover_landscape):
                    if is_copy_mode:
                        # 复制封面模式：直接复制文件而不添加数字
                        import shutil
                        shutil.copy(cover_landscape, landscape_cover_path)
                        log_func(f"[伪原创] 已复制横屏封面: {cover_landscape} -> {landscape_cover_path}")
                    else:
                        # 数字封面模式：使用generate_cover_with_cgpy生成带数字的封面
                        generate_cover_with_cgpy(cover_landscape, landscape_cover_path, 1, font_path)
                        log_func(f"[伪原创] 已生成横屏封面: {landscape_cover_path}")
                    cover_success = True
                else:
                    log_func(f"[伪原创] 【诊断】跳过横屏封面处理: cover_landscape存在={bool(cover_landscape)}, 文件存在={os.path.exists(cover_landscape) if cover_landscape else False}")
                
                # 处理竖屏封面
                if cover_portrait and os.path.exists(cover_portrait):
                    if is_copy_mode:
                        # 复制封面模式：直接复制文件而不添加数字
                        import shutil
                        shutil.copy(cover_portrait, portrait_cover_path)
                        log_func(f"[伪原创] 已复制竖屏封面: {cover_portrait} -> {portrait_cover_path}")
                    else:
                        # 数字封面模式：使用generate_cover_with_cgpy生成带数字的封面
                        generate_cover_with_cgpy(cover_portrait, portrait_cover_path, 1, font_path)
                        log_func(f"[伪原创] 已生成竖屏封面: {portrait_cover_path}")
                    cover_success = True
                else:
                    log_func(f"[伪原创] 【诊断】跳过竖屏封面处理: cover_portrait存在={bool(cover_portrait)}, 文件存在={os.path.exists(cover_portrait) if cover_portrait else False}")
                
                if cover_success:
                    if log_func:
                        log_func(f"[伪原创] 封面生成成功: 横屏={landscape_cover_path if os.path.exists(landscape_cover_path) else '无'}, 竖屏={portrait_cover_path if os.path.exists(portrait_cover_path) else '无'}")
                else:
                    if log_func:
                        log_func("[伪原创] 封面源文件不存在，跳过封面生成")
            else:
                if log_func:
                    log_func(f"[伪原创] 封面功能未启用 (模式: {cover_mode}) 或未提供封面源文件")
                    log_func(f"[伪原创] 【诊断】封面处理条件: is_generate_mode={is_generate_mode}, is_copy_mode={is_copy_mode}, cover_landscape存在={bool(cover_landscape)}, cover_portrait存在={bool(cover_portrait)}")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创] 封面生成过程中出错: {e}")
                import traceback
                log_func(f"[伪原创] 详细错误: {traceback.format_exc()}")
        
        # 生成功能汇总
        add_summary_output(log_func)

        # 设置返回值
        result["success"] = True
        result["output"] = output_path
        result["steps_completed"] = 11
        
        # 记录最终输出文件名
        status_manager.log_detail(f"[伪原创] 最终输出文件: {output_path}")

    except Exception as e:
        import traceback
        if log_func:
            log_func(f"[伪原创] 全局异常: {e}\n{traceback.format_exc()}")
        result["success"] = False
        result["error"] = str(e)

    finally:
        try:
            # 记录需要保留的文件路径（最终视频和封面）
            files_to_keep = [output_path]
            
            # 查找并添加封面文件到保留列表
            output_dir = os.path.dirname(output_path) if output_path else ""
            if output_dir and os.path.exists(output_dir):
                for file in os.listdir(output_dir):
                    if ("横屏封面" in file or "竖屏封面" in file) and (file.endswith(".png") or file.endswith(".jpg") or file.endswith(".jpeg")):
                        files_to_keep.append(os.path.join(output_dir, file))
            
            # 从临时文件列表中移除需要保留的文件
            for file_to_keep in files_to_keep:
                if file_to_keep in global_temp_files:
                    global_temp_files.remove(file_to_keep)
                    if log_callback:
                        log_callback(f"[伪原创] 保留文件: {os.path.basename(file_to_keep)}")
        except Exception as e:
            if log_callback:
                log_callback(f"[伪原创] 处理保留文件时出错: {e}")
                
        # 无论成功失败都会执行的清理逻辑
        cleanup_temp_files_on_failure(global_temp_files, result.get("success", False))

    return result


def generate_decorative_elements(
        element_type="logo", position="top_right", size=50):
    """
    生成装饰元素
    element_type: logo(logo), icon(图标), text(文字), shape(形状)
    position: top_left, top_right, bottom_left, bottom_right, center
    size: 元素大小(像素)
    """
    # 合法表达式模板
    elements = {
        "logo": {
            "filter": f"drawbox=x={{x}}:y={{y}}:w={size}:h={size}:color=white@0.02:t=fill",
            "description": "Logo装饰"
        },
        "icon": {
            "filter": f"drawbox=x={{x}}:y={{y}}:w={size}:h={size}:color=gold@0.02:t=fill",
            "description": "图标装饰"
        },
        "text": {
            "filter": f"drawbox=x={{x}}:y={{y}}:w={size*2}:h={size}:color=blue@0.02:t=fill",
            "description": "文字装饰"
        },
        "shape": {
            "filter": f"drawbox=x={{x}}:y={{y}}:w={size}:h={size}:color=red@0.02:t=fill",
            "description": "形状装饰"
        }
    }
    if element_type not in elements:
        element_type = "logo"
    element = elements[element_type]
    # 位置表达式
    if element_type == "text":
        # 文字居中或四角 - 修复: 不再使用text_w和text_h变量
        text_width = size * 2  # 估算文字宽度
        text_height = size     # 估算文字高度
        if position == "top_left":
            x = 10
            y = 10
        elif position == "top_right":
            x = f"w-{text_width}-10"  # 使用计算的文字宽度代替text_w
            y = 10
        elif position == "bottom_left":
            x = 10
            y = f"h-{text_height}-10" # 使用计算的文字高度代替text_h
        elif position == "bottom_right":
            x = f"w-{text_width}-10"  # 使用计算的文字宽度代替text_w
            y = f"h-{text_height}-10" # 使用计算的文字高度代替text_h
        else:  # center
            x = f"(w-{text_width})/2"  # 使用计算的文字宽度代替text_w
            y = f"(h-{text_height})/2" # 使用计算的文字高度代替text_h
        filter_str = element["filter"].replace(
            "{x}", str(x)).replace("{y}", str(y))
    else:
        # box类元素
        if position == "top_left":
            x = 10
            y = 10
        elif position == "top_right":
            x = f"w-{size}-10"
            y = 10
        elif position == "bottom_left":
            x = 10
            y = f"h-{size}-10"
        elif position == "bottom_right":
            x = f"w-{size}-10"
            y = f"h-{size}-10"
        else:  # center
            x = f"(w-{size})/2"
            y = f"(h-{size})/2"
        filter_str = element["filter"].replace(
            "{x}", str(x)).replace("{y}", str(y))
    return {
        "filter": filter_str,
        "description": element["description"],
        "type": element_type,
        "position": position,
        "size": size
    }


def generate_background_texture(texture_type="noise", intensity=0.3):
    """
    生成背景纹理
    texture_type: noise(噪点), pattern(图案), gradient(渐变), sparkle(闪烁)
    intensity: 纹理强度 0.1-0.8
    """
    textures = {
        "noise": {
            # 降低噪点强度
            "filter": f"noise=alls={max(1,int(intensity*10))}:allf=t",
            "description": "噪点纹理"
        },
        "pattern": {
            "filter": f"noise=alls={max(1,int(intensity*6))}:allf=t",
            "description": "图案纹理"
        },
        "gradient": {
            "filter": f"noise=alls={max(1,int(intensity*4))}:allf=t",
            "description": "渐变纹理"
        },
        "sparkle": {
            "filter": f"noise=alls={max(1,int(intensity*8))}:allf=t",
            "description": "闪烁纹理"
        }
    }

    if texture_type not in textures:
        texture_type = "noise"

    texture = textures[texture_type]

    return {
        "filter": texture["filter"],
        "description": texture["description"],
        "type": texture_type,
        "intensity": intensity
    }


def apply_dynamic_decorations(
        video_path, output_path, decorations_config, log_func=None):
    """应用动态装饰效果"""
    if not decorations_config:
        if log_func:
            log_func("[动态装饰] 无装饰配置，跳过处理")
        return False



    try:
        if log_func:
            log_func(f"[动态装饰] 开始应用装饰效果: {decorations_config}")

        # 构建装饰滤镜链
        decoration_filters = []

        # 应用装饰元素
        if decorations_config.get("elements"):
            for element_config in decorations_config["elements"]:
                element = generate_decorative_elements(
                    element_config["type"],
                    element_config["position"],
                    element_config["size"]
                )
                if element["filter"] and element["filter"].strip():  # 确保滤镜不为空
                    decoration_filters.append(element["filter"])
                    if log_func:
                        log_func(f"[动态装饰] 添加装饰元素: {element['description']}")

        # 应用背景纹理
        if decorations_config.get("texture"):
            texture = generate_background_texture(
                decorations_config["texture"]["type"],
                decorations_config["texture"]["intensity"]
            )
            if texture["filter"] and texture["filter"].strip():  # 确保滤镜不为空
                decoration_filters.append(texture["filter"])
                if log_func:
                    log_func(f"[动态装饰] 添加背景纹理: {texture['description']}")

        # 构建完整滤镜链
        if decoration_filters:
            # 过滤掉空字符串
            decoration_filters = [
                f for f in decoration_filters if f and f.strip()]
            if not decoration_filters:
                if log_func:
                    log_func(f"[动态装饰] 所有滤镜均为空，跳过装饰应用")
                import shutil
                shutil.copy2(video_path, output_path)
                return True

            vf_chain = ",".join(decoration_filters)

            # 检查硬件加速支持
            try:
                encoders_cmd = ['ffmpeg', '-encoders']
                encoders_check = subprocess.run(encoders_cmd,
                                              stdout=subprocess.PIPE,
                                              stderr=subprocess.PIPE,
                                              timeout=5)

                use_decoration_hardware = False
                if encoders_check.returncode == 0:
                    encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                    if 'h264_nvenc' in encoders_output:
                        use_decoration_hardware = True
                        if log_func:
                            log_func("[动态装饰] 启用CUDA硬件加速")
                    else:
                        if log_func:
                            log_func("[动态装饰] 使用CPU编码")
                else:
                    if log_func:
                        log_func("[动态装饰] 使用CPU编码")
            except Exception as e:
                use_decoration_hardware = False
                if log_func:
                    log_func(f"[动态装饰] 硬件加速检测异常，使用CPU编码")

            if use_decoration_hardware:
                cmd = [
                    'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', video_path,
                    '-vf', vf_chain,
                    '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p',
                    '-b:v', '15000k', '-preset', 'p1',
                    '-c:a', 'copy',
                    output_path
                ]
            else:
                cmd = [
                    'ffmpeg', '-y', '-i', video_path,
                    '-vf', vf_chain,
                    '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                    '-b:v', '15000k', '-preset', 'medium',
                    '-c:a', 'copy',
                    output_path
                ]

            if log_func:
                log_func(f"[动态装饰] 执行命令: {' '.join(cmd)}")

            proc_result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

            if proc_result.returncode == 0:
                if log_func:
                    log_func(f"[动态装饰] 装饰效果应用成功: {output_path}")
                return True
            else:
                if log_func:
                    log_func(f"[动态装饰] 装饰效果应用失败")
                    log_func(f"[动态装饰] 错误详情: {proc_result.stderr}")
                    log_func(f"[动态装饰] 标准输出: {proc_result.stdout}")
                    log_func(f"[动态装饰] 命令: {' '.join(cmd)}")
                    log_func(f"[动态装饰] 滤镜链: {vf_chain}")
                    log_func(f"[动态装饰] 装饰配置: {decorations_config}")
                return False
        else:
            # 没有装饰，直接复制文件
            import shutil
            shutil.copy2(video_path, output_path)
            if log_func:
                log_func(f"[动态装饰] 无装饰效果，直接复制文件")
            return True

    except Exception as e:
        if log_func:
            log_func(f"[动态装饰] 装饰效果应用异常: {e}")
        return False


def apply_video_enhancement(video_path, output_path,
                            log_func=None, enable_rife=False):
    """
    实现第4步视频增强处理，包含RIFE、空间变形、超分辨率重建、视频帧插值、时间超分辨率、HDR转换和视频补帧等效果

    处理顺序：
    1. 视频补帧(RIFE) - 增加视频帧率，使动作更流畅（如果启用）
    2. 抽帧处理 - 随机删除部分帧，增加视觉变化
    3. 空间变形 - 应用微妙的镜头变形效果
    4. 超分辨率重建 - 提高画面分辨率和清晰度
    5. HDR转换 - 增强色彩动态范围
    6. 时间超分辨率 - 进一步优化时间维度上的细节

    参数:
    video_path: 输入视频路径
    output_path: 输出视频路径
    log_func: 日志输出函数
    enable_rife: 是否启用RIFE补帧（默认不启用）

    返回:
    bool: 处理成功返回True，否则返回False
    """
    # 导入json模块用于读取配置
    import json
    import random
    
    # 在try外部定义临时文件变量，以便在finally中清理
    temp_video_current = video_path  # 当前处理的输入文件
    temp_video_next = None
    temp_files_to_clean = []
    
    try:
        if log_func:
            log_func(f"[S4-视频增强] 开始应用第4步视频增强效果...")
            log_func(f"[S4-视频增强] 使用输入文件: {video_path}")

        # 首先检查硬件加速支持（所有步骤共用）
        try:
            encoders_cmd = ['ffmpeg', '-encoders']
            encoders_check = subprocess.run(encoders_cmd,
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          timeout=5)

            use_rife_hardware = False
            if encoders_check.returncode == 0:
                encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                if 'h264_nvenc' in encoders_output:
                    use_rife_hardware = True
                    if log_func:
                        log_func("[S4-视频增强] 启用CUDA硬件加速")
                else:
                    if log_func:
                        log_func("[S4-视频增强] 使用CPU编码")
            else:
                if log_func:
                    log_func("[S4-视频增强] 使用CPU编码")
        except Exception as e:
            use_rife_hardware = False
            if log_func:
                log_func(f"[S4-视频增强] 硬件加速检测异常，使用CPU编码")

        # 检查配置
        config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        if not enable_rife and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enable_rife = config.get('enable_rife', False)
                if log_func:
                    log_func(
                        f"[S4-视频增强] 从配置文件读取RIFE设置: {'启用' if enable_rife else '禁用'}")
            except Exception as e:
                if log_func:
                    log_func(f"[S4-视频增强] 读取配置文件失败: {str(e)}")

        # 临时文件路径
        temp_dir = os.path.dirname(video_path)
        
        # 使用临时文件来保存中间处理结果
            
        # 修改：使用临时文件来保存中间处理结果，只保留最终输出
        # 使用可预测的临时文件名，便于调试
        temp_counter = 1
        temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
        # 添加到清理列表
        temp_files_to_clean.append(temp_video_next)

        if log_func:
            log_func(f"[S4-视频增强] 创建临时处理文件: {os.path.basename(temp_video_next)}")
            log_func(f"[S4-视频增强] 临时文件完整路径: {temp_video_next}")
            log_func(f"[S4-视频增强] 临时文件存储目录: {temp_dir}")
            log_func(f"[S4-视频增强] 注意: 临时文件将在处理完成后自动清理")

        # 空间变形参数 - 降低强度以避免水印显示问题
        global warp_cx, warp_cy, warp_k1, warp_k2
        warp_cx = random.uniform(0.49, 0.51)  # 减少中心点偏移，避免倾斜
        warp_cy = random.uniform(0.49, 0.51)  # 减少中心点偏移，避免倾斜
        warp_k1 = random.uniform(-0.01, 0.01)  # 大幅降低径向畸变，避免水印裁切
        warp_k2 = random.uniform(-0.005, 0.005)  # 大幅降低径向畸变，避免水印裁切

        # 1. 视频补帧(RIFE)处理 - 将帧率提高到60fps
        if enable_rife:
            if log_func:
                log_func(f"[S4-视频增强] 步骤1/6: 应用RIFE视频补帧...")

            # 获取输入视频的帧率
            input_fps = get_video_fps(temp_video_current)
            if input_fps <= 0:
                input_fps = 30
                if log_func:
                    log_func(f"[S4-视频增强] 无法获取视频帧率，使用默认值：{input_fps}fps")
            else:
                if log_func:
                    log_func(f"[S4-视频增强] 检测到视频帧率：{input_fps}fps")

            # 随机选择输出帧率因子(190%-230%)
            fps_factor = random.uniform(1.9, 2.3)
            target_fps = input_fps * fps_factor

            # 确保帧率在合理范围内
            if target_fps < 24:
                target_fps = 24
            elif target_fps > 120:
                target_fps = 120

            if log_func:
                log_func(
                    f"[S4-视频增强] RIFE补帧目标帧率: {target_fps:.2f}fps (原始帧率的{fps_factor*100:.1f}%)")

            # RIFE补帧使用minterpolate滤镜进行智能插帧
            # 使用之前检测的硬件加速设置

            if use_rife_hardware:
                rife_cmd = [
                    'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                    '-vf', f'minterpolate=fps={target_fps:.2f}:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1',
                    '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]
            else:
                rife_cmd = [
                    'ffmpeg', '-y', '-i', temp_video_current,
                    '-vf', f'minterpolate=fps={target_fps:.2f}:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1',
                    '-c:v', 'libx264', '-preset', 'medium', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]

            if log_func:
                log_func(f"[S4-视频增强] RIFE命令: {' '.join(rife_cmd)}")

            process_rife = subprocess.run(rife_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

            if process_rife.returncode != 0:
                if log_func:
                    log_func(f"[S4-视频增强] RIFE补帧失败，错误: {process_rife.stderr}")
                    log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
                # 失败时立即返回失败
                return False
            else:
                if log_func:
                    log_func(f"[S4-视频增强] RIFE补帧完成")
                    
                # 验证输出文件是否包含视频流
                has_video = get_media_streams(temp_video_next, check_video_only=True)
                if not has_video:
                    if log_func:
                        log_func(f"[S4-视频增强] 错误: RIFE补帧处理后的文件不包含视频流，处理中止")
                    # 文件不包含视频流，视为失败
                    return False
                    
                # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
                temp_video_current = temp_video_next
                # 为下一步创建新的临时文件，避免覆盖任何现有文件
                temp_counter += 1
                temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
                # 确保新的临时文件被跟踪
                if temp_video_next not in temp_files_to_clean:
                    temp_files_to_clean.append(temp_video_next)

                if log_func:
                    log_func(f"[S4-视频增强] RIFE补帧完成，当前临时文件: {os.path.basename(temp_video_current)}")
                    if os.path.exists(temp_video_current):
                        file_size = os.path.getsize(temp_video_current) / (1024*1024)
                        log_func(f"[S4-视频增强] 临时文件大小: {file_size:.1f}MB")
        else:
            if log_func:
                log_func(f"[S4-视频增强] 跳过RIFE补帧（已禁用），继续后续处理...")

        # 2. 抽帧处理 - 从RIFE处理后的视频中随机删除部分帧
        if log_func:
            log_func(f"[伪原创4] 步骤2/6: 应用抽帧处理...")

        # 固定抽帧范围（5-10帧）
        global step2_insert_frames
        drop_frame_count = random.randint(5, 10)
        if log_func:
            if step2_insert_frames > 0:
                log_func(f"[伪原创4] 第2步插入了{step2_insert_frames}帧，第4步抽帧数量：{drop_frame_count}帧")
            else:
                log_func(f"[伪原创4] 第2步未插帧，第4步抽帧数量：{drop_frame_count}帧")
        
        # 获取输入视频的帧率
        input_fps = get_video_fps(temp_video_current)
        if input_fps <= 0:
            input_fps = 30
            if log_func:
                log_func(f"[伪原创4] 无法获取视频帧率，使用默认值：{input_fps}fps")
        else:
            if log_func:
                log_func(f"[伪原创4] 检测到视频帧率：{input_fps}fps")

        try:
            # 使用伪原创主函数中定义的get_duration函数
            from status_manager import status_manager
            info = status_manager.get_media_info(temp_video_current)
            video_duration = info["duration"] if info["is_valid"] else 200
            if log_func:
                log_func(f"[伪原创4] 检测到视频时长: {video_duration:.2f}秒")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创4] 无法获取视频时长，使用默认值: {str(e)}")
            video_duration = 200  # 默认值

        if video_duration > 0:
            # 将视频分成n个区间（n=抽帧数量的1.8倍）
            drop_intervals = int(drop_frame_count * 1.8)
            drop_segment_duration = video_duration / drop_intervals
            
            # 随机选择区间
            drop_segments = random.sample(range(drop_intervals), drop_frame_count)
            drop_segments.sort()
            
            # 在每个选定区间中随机选择一个时间点
            drop_time_points = []
            for segment in drop_segments:
                start_time = segment * drop_segment_duration
                end_time = (segment + 1) * drop_segment_duration
                drop_time = random.uniform(start_time, end_time)
                drop_time_points.append(drop_time)

            # 按时间排序
            drop_time_points.sort()

            # 构建select过滤器字符串，排除特定时间点的帧
            drop_filter = "select='"
            # 添加一个基础条件，确保至少有一些帧被保留
            drop_filter += "eq(n,0)+gte(n,0)"  # 确保第一帧被保留，并且所有帧都被考虑
            for drop_time in drop_time_points:
                frame_number = int(drop_time * input_fps)
                drop_filter += f"*not(eq(n,{frame_number}))"
            drop_filter += "',setpts=N/FRAME_RATE/TB"

            # 应用抽帧滤镜，使用与RIFE相同的硬件加速策略
            if use_rife_hardware:
                drop_cmd = [
                    'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                    '-vf', drop_filter,
                    '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]
            else:
                drop_cmd = [
                    'ffmpeg', '-y', '-i', temp_video_current,
                    '-vf', drop_filter,
                    '-c:v', 'libx264', '-preset', 'medium', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]

            if log_func:
                log_func(f"[S4-视频增强] 抽帧命令: {' '.join(drop_cmd)}")
                log_func(f"[S4-视频增强] 使用select过滤器进行随机抽帧，选择了{len(drop_time_points)}个时间点进行帧删除")
                log_func(f"[S4-视频增强] 抽帧时间点（秒）: {', '.join([f'{t:.2f}' for t in drop_time_points])}")

            process_drop = subprocess.run(drop_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

            if process_drop.returncode != 0:
                if log_func:
                    log_func(f"[S4-视频增强] 抽帧处理失败，错误: {process_drop.stderr}")
                    log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
                # 失败时立即返回失败
                return False
            else:
                if log_func:
                    log_func(f"[S4-视频增强] 抽帧处理完成")
                
                # 验证输出文件是否包含视频流
                has_video = get_media_streams(temp_video_next, check_video_only=True)
                if not has_video:
                    if log_func:
                        log_func(f"[S4-视频增强] 错误: 抽帧处理后的文件不包含视频流，处理中止")
                    # 文件不包含视频流，视为失败
                    return False
                
                # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
                temp_video_current = temp_video_next
                # 为下一步创建新的临时文件，避免覆盖任何现有文件
                temp_counter += 1
                temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
                # 确保新的临时文件被跟踪
                if temp_video_next not in temp_files_to_clean:
                    temp_files_to_clean.append(temp_video_next)
        else:
            # 如果无法获取视频时长，使用简单的概率方式抽帧
            drop_filter = f"select='eq(n,0)+gte(n,0)*not(gte(random(0),0.99))',setpts=N/FRAME_RATE/TB"
            
            if use_rife_hardware:
                drop_cmd = [
                    'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                    '-vf', drop_filter,
                    '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]
            else:
                drop_cmd = [
                    'ffmpeg', '-y', '-i', temp_video_current,
                    '-vf', drop_filter,
                    '-c:v', 'libx264', '-preset', 'medium', '-b:v', '20000k',
                    '-c:a', 'copy',
                    '-map', '0:v:0',                    # 明确映射视频流
                    '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                    '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                    '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                    '-async', '1',                      # 音频同步参数
                    '-copyts',                          # 复制原始时间戳
                    temp_video_next
                ]
            
            if log_func:
                log_func(f"[S4-视频增强] 无法获取视频时长，使用随机概率方式抽帧")
                log_func(f"[S4-视频增强] 抽帧命令: {' '.join(drop_cmd)}")
                
            process_drop = subprocess.run(drop_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
            
            if process_drop.returncode != 0 or not check_video_streams(temp_video_next):
                if log_func:
                    log_func(f"[S4-视频增强] 抽帧处理失败，处理中止")
                # 处理失败，立即返回
                return False
            else:
                if log_func:
                    log_func(f"[S4-视频增强] 抽帧处理完成")
                # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
                temp_video_current = temp_video_next
                # 为下一步创建新的临时文件，避免覆盖任何现有文件
                temp_counter += 1
                temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
                # 确保新的临时文件被跟踪
                if temp_video_next not in temp_files_to_clean:
                    temp_files_to_clean.append(temp_video_next)

        # 3. 空间变形处理 - 应用微妙的镜头变形效果
        if log_func:
            log_func(f"[S4-视频增强] 步骤3/6: 应用空间变形...")

        # 应用lenscorrection滤镜进行空间变形，使用与RIFE相同的硬件加速策略
        if use_rife_hardware:
            warp_cmd = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                '-vf', f"lenscorrection=cx={warp_cx:.4f}:cy={warp_cy:.4f}:k1={warp_k1:.4f}:k2={warp_k2:.4f}",
                '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '20000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_video_next
            ]
        else:
            warp_cmd = [
                'ffmpeg', '-y', '-i', temp_video_current,
                '-vf', f"lenscorrection=cx={warp_cx:.4f}:cy={warp_cy:.4f}:k1={warp_k1:.4f}:k2={warp_k2:.4f}",
                '-c:v', 'libx264', '-preset', 'medium', '-b:v', '20000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_video_next
            ]

        if log_func:
            log_func(f"空间变形命令: {' '.join(warp_cmd)}")

        process_warp = subprocess.run(warp_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if process_warp.returncode != 0:
            if log_func:
                log_func(f"[S4-视频增强] 空间变形处理失败，错误: {process_warp.stderr}")
                log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
            # 失败时立即返回失败
            return False
        else:
            if log_func:
                log_func(
                    f"[S4-视频增强] 空间变形完成，参数: cx={warp_cx:.4f}, cy={warp_cy:.4f}, k1={warp_k1:.4f}, k2={warp_k2:.4f}")
                
            # 验证输出文件是否包含视频流
            has_video = get_media_streams(temp_video_next, check_video_only=True)
            if not has_video:
                if log_func:
                    log_func(f"[S4-视频增强] 错误: 空间变形处理后的文件不包含视频流，处理中止")
                # 文件不包含视频流，视为失败
                return False
                
            # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
            temp_video_current = temp_video_next
            # 为下一步创建新的临时文件，避免覆盖任何现有文件
            temp_counter += 1
            temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
            # 确保新的临时文件被跟踪
            if temp_video_next not in temp_files_to_clean:
                temp_files_to_clean.append(temp_video_next)

        # 4. 超分辨率重建 - 使用Lanczos算法进行放大，然后重新标准化回1920x1080
        if log_func:
            log_func(f"[S4-视频增强] 步骤4/6: 应用超分辨率重建...")

        if use_rife_hardware:
            sr_cmd = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                '-vf', 'scale=iw*1.5:ih*1.5:flags=lanczos,scale=1920:1080:flags=lanczos',
                '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '25000k',
                '-c:a', 'copy',
                temp_video_next
            ]
        else:
            sr_cmd = [
                'ffmpeg', '-y', '-i', temp_video_current,
                '-vf', 'scale=iw*1.5:ih*1.5:flags=lanczos,scale=1920:1080:flags=lanczos',
                '-c:v', 'libx264', '-preset', 'medium', '-b:v', '25000k',
                '-c:a', 'copy',
                temp_video_next
            ]

        if log_func:
            log_func(f"[S4-视频增强] 超分辨率命令: {' '.join(sr_cmd)}")

        process_sr = subprocess.run(sr_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if process_sr.returncode != 0:
            if log_func:
                log_func(f"[S4-视频增强] 超分辨率重建失败，错误: {process_sr.stderr}")
                log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
            # 失败时立即返回失败
            return False
        else:
            if log_func:
                log_func(f"[S4-视频增强] 超分辨率重建完成")
                
            # 验证输出文件是否包含视频流
            has_video = get_media_streams(temp_video_next, check_video_only=True)
            if not has_video:
                if log_func:
                    log_func(f"[S4-视频增强] 错误: 超分辨率重建后的文件不包含视频流，处理中止")
                # 文件不包含视频流，视为失败
                return False
                
            # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
            temp_video_current = temp_video_next
            # 为下一步创建新的临时文件，避免覆盖任何现有文件
            temp_counter += 1
            temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
            # 确保新的临时文件被跟踪
            if temp_video_next not in temp_files_to_clean:
                temp_files_to_clean.append(temp_video_next)

        # 5. HDR转换 - 使用简化版的HDR模拟效果，避免使用zscale
        if log_func:
            log_func(f"[S4-视频增强] 步骤5/6: 应用HDR转换...")

        if use_rife_hardware:
            hdr_cmd = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                '-vf', 'eq=brightness=0.03:contrast=1.1:saturation=1.1:gamma=1.05',
                '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '25000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_video_next
            ]
        else:
            hdr_cmd = [
                'ffmpeg', '-y', '-i', temp_video_current,
                '-vf', 'eq=brightness=0.03:contrast=1.1:saturation=1.1:gamma=1.05',
                '-c:v', 'libx264', '-preset', 'medium', '-b:v', '25000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                temp_video_next
            ]

        if log_func:
            log_func(f"[S4-视频增强] HDR转换命令: {' '.join(hdr_cmd)}")

        process_hdr = subprocess.run(hdr_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if process_hdr.returncode != 0:
            if log_func:
                log_func(f"[S4-视频增强] HDR转换失败，错误: {process_hdr.stderr}")
                log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
            # 失败时立即返回失败
            return False
        else:
            if log_func:
                log_func(f"[S4-视频增强] HDR转换完成")
                
            # 验证输出文件是否包含视频流
            has_video = get_media_streams(temp_video_next, check_video_only=True)
            if not has_video:
                if log_func:
                    log_func(f"[S4-视频增强] 错误: HDR转换后的文件不包含视频流，处理中止")
                # 文件不包含视频流，视为失败
                return False
                
            # 更新当前处理文件路径 - 修复：确保不会覆盖原始输入文件
            temp_video_current = temp_video_next
            # 为下一步创建新的临时文件，避免覆盖任何现有文件
            temp_counter += 1
            temp_video_next = os.path.join(temp_dir, f"step4_temp_{temp_counter:04d}.mp4")
            # 确保新的临时文件被跟踪
            if temp_video_next not in temp_files_to_clean:
                temp_files_to_clean.append(temp_video_next)

        # 6. 时间超分辨率 - 应用时间维度上的细节增强
        if log_func:
            log_func(f"[S4-视频增强] 步骤6/6: 应用时间超分辨率...")

        # 使用tblend滤镜进行时间维度上的细节增强
        if use_rife_hardware:
            tsr_cmd = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_video_current,
                '-vf', 'tblend=all_mode=average',
                '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '25000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                output_path  # 直接输出到最终文件
            ]
        else:
            tsr_cmd = [
                'ffmpeg', '-y', '-i', temp_video_current,
                '-vf', 'tblend=all_mode=average',
                '-c:v', 'libx264', '-preset', 'medium', '-b:v', '25000k',
                '-c:a', 'copy',
                '-map', '0:v:0',                    # 明确映射视频流
                '-map', '0:a:0?',                   # 明确映射音频流（如果存在）
                '-avoid_negative_ts', 'disabled',   # 禁用负时间戳处理，保持原始时间戳
                '-vsync', 'passthrough',            # 保持原始帧率，不强制改变
                '-async', '1',                      # 音频同步参数
                '-copyts',                          # 复制原始时间戳
                output_path  # 直接输出到最终文件
            ]

        if log_func:
            log_func(f"[S4-视频增强] 时间超分辨率命令: {' '.join(tsr_cmd)}")

        process_tsr = subprocess.run(tsr_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if process_tsr.returncode != 0:
            if log_func:
                log_func(f"[S4-视频增强] 时间超分辨率失败，错误: {process_tsr.stderr}")
                log_func(f"[S4-视频增强] 处理中止，视频增强步骤失败")
            # 失败时立即返回失败
            return False
        else:
            if log_func:
                log_func(f"[S4-视频增强] 时间超分辨率完成")
                
                # 验证输出文件是否包含视频流
                has_video = get_media_streams(output_path, check_video_only=True)
                if not has_video:
                    if log_func:
                        log_func(f"[S4-视频增强] 错误: 时间超分辨率处理后的文件不包含视频流，处理中止")
                    # 文件不包含视频流，视为失败
                    return False

        # 确保最终输出文件正确生成
        if temp_video_current != output_path:
            try:
                import shutil
                shutil.copy2(temp_video_current, output_path)
                if log_func:
                    log_func(f"[S4-视频增强] 复制最终结果: {os.path.basename(temp_video_current)} → {os.path.basename(output_path)}")
            except Exception as copy_error:
                if log_func:
                    log_func(f"[S4-视频增强] 复制最终结果失败: {copy_error}")
                return False

        if log_func:
            log_func(f"[S4-视频增强] 所有视频增强处理完成，最终输出: {output_path}")

        # 处理成功
        return True

    except Exception as e:
        if log_func:
            log_func(f"[S4-视频增强] 处理异常: {str(e)}")
            import traceback
            log_func(f"[S4-视频增强] 异常详情: {traceback.format_exc()}")
        return False
        
    finally:
        # 清理第4步内部临时文件 - 保留主流程需要的输出文件
        if log_func:
            log_func(f"[S4-视频增强] 开始清理第4步内部临时文件，共 {len(temp_files_to_clean)} 个文件")
            log_func(f"[S4-视频增强] 注意: 保留主流程输出文件 {os.path.basename(output_path)} 供后续步骤使用")

        # 只清理内部临时文件，不清理主流程文件
        cleaned_count = 0
        for f in temp_files_to_clean:
            if os.path.exists(f) and f != video_path and f != output_path:  # 避免删除输入和输出文件
                try:
                    file_size = os.path.getsize(f) / (1024*1024)  # MB
                    os.remove(f)
                    cleaned_count += 1
                    if log_func:
                        log_func(f"[S4-视频增强] ✅ 已清理内部临时文件: {os.path.basename(f)} ({file_size:.1f}MB)")
                except Exception as e:
                    if log_func:
                        log_func(f"[S4-视频增强] ❌ 临时文件清理失败: {os.path.basename(f)}, {str(e)}")

        if log_func:
            log_func(f"[S4-视频增强] 第4步内部临时文件清理完成，共清理 {cleaned_count} 个文件")
            log_func(f"[S4-视频增强] 主流程文件 {os.path.basename(output_path)} 已保留，将在整个处理完成后统一清理")


@step_try_except(9, "编码转换")
def apply_codec_conversion(video_path, output_path, log_func=None):
    """
    实现第9步编码转换处理，将视频从H.264转换为H.265，并应用高级像素格式

    处理内容：
    1. 将视频编码从H.264转换为H.265(HEVC)
    2. 随机选择高级像素格式(yuv420p10le/yuv422p/yuv422p10le)
    3. 优化比特率设置

    参数:
    video_path: 输入视频路径
    output_path: 输出视频路径
    log_func: 日志输出函数

    返回:
    bool: 处理成功返回True，否则返回False
    """
    # 创建结果对象
    result = {"success": True, "error": None}
    
    # 验证输入文件
    is_valid_input, _ = step_exception_handler.verify_step_input(
        9, "编码转换", video_path, result)
    
    if not is_valid_input:
        return False
    
    if log_func:
        log_func(f"[伪原创9] 开始应用第9步编码转换...")
        log_func(f"[伪原创9] 使用输入文件: {video_path}")

    # 随机选择一种高级像素格式(排除yuv420p)
    pixel_formats = ["yuv420p10le", "yuv422p", "yuv422p10le"]
    selected_format = random.choice(pixel_formats)

    # 全局变量以便在summary中使用
    global selected_pixel_format
    selected_pixel_format = selected_format

    # 根据像素格式调整比特率
    # 10位格式和yuv422需要更高的比特率以保持质量
    if "10le" in selected_format:
        bitrate = "18000k"  # 10位格式
    elif "422" in selected_format:
        bitrate = "20000k"  # 422格式(更多色彩数据)
    else:
        bitrate = "15000k"  # 默认

    if log_func:
        log_func(f"[伪原创9] 选择像素格式: {selected_format}, 比特率: {bitrate}")

    # 使用HEVC(H.265)编码器和选定的像素格式
    # 需要从全局变量获取硬件加速状态，或者检查编码器支持
    try:
        # 检查是否支持HEVC硬件编码器
        encoders_cmd = ['ffmpeg', '-encoders']
        encoders_check = subprocess.run(encoders_cmd,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      timeout=5)

        use_hevc_hardware = False
        if encoders_check.returncode == 0:
            encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
            if 'hevc_nvenc' in encoders_output:
                use_hevc_hardware = True
                if log_func:
                    log_func("[伪原创9] FFmpeg支持hevc_nvenc编码器，启用CUDA硬件加速")
            else:
                if log_func:
                    log_func("[伪原创9] FFmpeg不支持hevc_nvenc编码器，将使用CPU编码")
        else:
            if log_func:
                log_func("[伪原创9] 无法检查FFmpeg编码器支持，将使用CPU编码")
    except Exception as e:
        use_hevc_hardware = False
        if log_func:
            log_func(f"[伪原创9] 硬件加速检测异常: {str(e)[:100]}")
            log_func("[伪原创9] 将使用CPU编码")

    if use_hevc_hardware:
        hevc_cmd = [
            'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', video_path,
            '-c:v', 'hevc_nvenc', '-preset', 'p1', '-b:v', bitrate,
            '-pix_fmt', selected_format,
            '-c:a', 'copy',
            output_path
        ]
    else:
        hevc_cmd = [
            'ffmpeg', '-y', '-i', video_path,
            '-c:v', 'libx265', '-preset', 'medium', '-b:v', bitrate,
            '-pix_fmt', selected_format,
            '-c:a', 'copy',
            output_path
        ]

    if log_func:
        log_func(f"[伪原创9] 执行命令: {' '.join(hevc_cmd)}")
    
    status_manager.log_command(9, hevc_cmd)
    status_manager.log_progress(9, 25)  # 开始编码

    try:
        # 使用Popen而不是run，以便正确处理和显示进度
        process_hevc = subprocess.Popen(
            hevc_cmd,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace',
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)

        stderr_output = ""
        stdout_output = ""

        for line in process_hevc.stderr:
            stderr_output += line
            if log_func and "frame=" in line:
                log_func(f"[伪原创9][ffmpeg] {line.strip()}")
                # 解析进度并更新
                try:
                    if "time=" in line:
                        time_str = line.split("time=")[1].split()[0]
                        time_parts = time_str.split(":")
                        if len(time_parts) >= 3:
                            current_time = float(time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
                            # 假设总时长为200秒
                            progress = min(int(current_time / 200 * 100), 75)
                            status_manager.log_progress(9, 25 + progress // 2)
                except Exception:
                    pass

        for line in process_hevc.stdout:
            stdout_output += line

        process_hevc.wait()
        status_manager.log_progress(9, 75)  # 编码完成

        if process_hevc.returncode != 0:
            # 使用专门的处理执行错误异常
            raise ProcessExecutionError(
                f"编码转换失败，返回码: {process_hevc.returncode}", 
                9,
                details=stderr_output,
                command=hevc_cmd
            )
    except ProcessExecutionError as e:
        # 编码转换失败，直接抛出异常，不进行智能回退
        if log_func:
            log_func(f"[伪原创9] 编码转换失败，处理中止")

        # 记录失败信息
        status_manager.complete_step(
            "编码转换", 9, TOTAL_STEPS,
            success=False,
            details=f"编码转换失败: {str(e)}",
            output_file=None
        )

        # 重新抛出异常，让上层处理
        raise
    
    # 验证输出文件
    is_valid_output, _ = step_exception_handler.verify_step_output(
        9, "编码转换", output_path, hevc_cmd, result, video_path)
    
    if not is_valid_output and not result.get("recovered", False):
        # 如果输出文件无效，直接抛出异常，不进行智能回退
        error_msg = "编码转换输出文件无效"
        status_manager.complete_step(
            "编码转换", 9, TOTAL_STEPS,
            success=False,
            details=error_msg,
            output_file=None
        )

        # 抛出异常让上层处理
        raise ProcessExecutionError(error_msg, 9)
    
    # 处理成功或已恢复
    if log_func:
        log_func(f"[伪原创9] 成功将视频转换为H.265编码，像素格式: {selected_format}")
        log_func(f"[伪原创9] 生成文件: {output_path}")

    # 获取转换前后的文件大小，计算节省的空间
    try:
        original_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
        converted_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        size_reduction = (original_size - converted_size) / original_size * 100  # 百分比

        if log_func:
            log_func(f"[伪原创9] 原始文件大小: {original_size:.2f} MB")
            log_func(f"[伪原创9] 转换后文件大小: {converted_size:.2f} MB")
            log_func(f"[伪原创9] 文件大小减少: {size_reduction:.2f}%")
    except Exception as e:
        # 这只是一个非关键错误，记录但不中断流程
        if log_func:
            log_func(f"[伪原创9] 计算文件大小时出错: {e}")
        size_reduction = 0

    # 更新状态
    status_manager.log_progress(9, 100)  # 全部完成
    status_manager.complete_step(
        "编码转换", 9, TOTAL_STEPS, 
        success=True, 
        details=f"H.265编码，像素格式: {selected_format}，节省空间: {size_reduction:.2f}%", 
        output_file=output_path
    )
    
    return True


# 添加到 pseudo_original_ffmpeg_two_step 函数结尾处的 finally 块
def add_summary_output(log_func=None):
    """在伪原创处理结束时添加功能汇总输出"""
    try:
        # 设置日志函数并输出汇总
        status_manager.set_log_func(log_func)
        status_manager.output_summary()
    except Exception as e:
        import traceback
        if log_func:
            log_func(f"[伪原创] 生成汇总日志时出错: {str(e)}")
            log_func(f"[伪原创] 错误详情: {traceback.format_exc()}")






def get_video_fps(video_path):
    """获取视频的帧率

    Args:
        video_path: 视频文件路径

    Returns:
        float: 视频帧率，如果无法获取则返回0
    """
    try:
        # 使用status_manager的get_media_info获取视频信息
        info = status_manager.get_media_info(video_path)

        if info["is_valid"] and info["fps"] != "未知":
            # info["fps"]可能是字符串格式，需要转换为float
            try:
                return float(info["fps"])
            except (ValueError, TypeError):
                return 0
        return 0
    except Exception as e:
        print(f"获取视频帧率失败: {str(e)}")
        return 0

@step_try_except(11, "MP4头部修复")
def apply_mp4_header_fix(video_path, output_path=None, log_func=None):
    """
    应用MP4头部修复
    视频正片结束后，复制MP4头部到文件前部，以优化网络播放
    """
    try:
        if log_func:
            log_func(f"[伪原创] Step 11/11: MP4头部修复开始...")
            log_func(f"[伪原创] 输入文件: {video_path}")

        if output_path is None:
            output_path = os.path.splitext(video_path)[0] + ".fixed.mp4"

        cmd = [
            'ffmpeg', '-y', '-i', video_path,
            '-c', 'copy',
            '-movflags', '+faststart', 
            '-fflags', '+genpts',
            output_path
        ]

        if log_func:
            log_func(f"[DEBUG] MP4头部修复命令: {' '.join(cmd)}")

        # 修改这里，不要使用'result'作为变量名
        proc_result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')

        if proc_result.returncode != 0:
            if log_func:
                log_func(f"[伪原创] MP4头部修复失败: {proc_result.stderr}")
            return False
        else:
            if log_func:
                log_func(f"[伪原创] MP4头部修复成功: {output_path}")
            return True
    except Exception as e:
        if log_func:
            log_func(f"[伪原创] MP4头部修复异常: {str(e)}")
        return False




























































































def execute_decorations_as_step6(temp_step5, temp_step6, log_func, status_manager):
    """执行动态装饰效果作为第6步"""
    # 生成动态装饰配置
    decorations_config = {}

    # 随机决定是否应用动态装饰效果
    apply_decorations = random.random() < 0.7  # 70%的概率应用装饰效果

    if apply_decorations:
        # 强制每次都添加动态边框、装饰元素和背景纹理（可全部随机，但概率100%）
        border_types = ["gradient", "sparkle", "wave", "pulse", "neon"]
        border_type = random.choice(border_types)
        border_colors = [
            "rainbow", "white", "gold", "blue", "red", "green", "purple", "orange"
        ]
        border_color = random.choice(border_colors)
        border_width = random.randint(5, 20)
        border_speed = random.uniform(1.0, 4.0)
        decorations_config["border"] = {
            "type": border_type,
            "color": border_color,
            "width": border_width,
            "speed": border_speed
        }
        if log_func:
            log_func(f"[伪原创] Step 6/11 选择动态边框: {border_type}, 颜色={border_color}, 宽度={border_width}px")

        # 强制每次都添加1-3个装饰元素
        element_count = random.randint(1, 2)
        elements = []
        element_types = ["logo", "icon", "text", "shape"]
        element_positions = ["top_left", "top_right", "bottom_left", "bottom_right", "center"]

        for _ in range(element_count):
            element_type = random.choice(element_types)
            element_position = random.choice(element_positions)
            element_size = random.randint(30, 80)
            elements.append({
                "type": element_type,
                "position": element_position,
                "size": element_size
            })

        decorations_config["elements"] = elements
        if log_func:
            log_func(f"[伪原创] Step 6/11 选择装饰元素: {element_count}个")

        # 强制每次都添加背景纹理
        texture_types = ["noise", "pattern", "gradient", "sparkle"]
        texture_type = random.choice(texture_types)
        texture_intensity = random.uniform(0.01, 0.03)
        decorations_config["texture"] = {
            "type": texture_type,
            "intensity": texture_intensity
        }
        if log_func:
            log_func(f"[伪原创] Step 6/11 选择背景纹理: {texture_type}, 强度={texture_intensity:.2f}")

        status_manager.log_progress(6, 50)

        # 应用装饰效果
        try:
            # 使用内置的装饰效果应用函数
            success = apply_dynamic_decorations(temp_step5, temp_step6, decorations_config, log_func)

            if success and os.path.exists(temp_step6):
                if log_func:
                    log_func(f"[伪原创] Step 6/11 动态装饰应用成功，装饰数量: {len(decorations_config)}，输出: {temp_step6}")

                # 更新功能状态
                success_details = f"边框={border_type}, 元素={len(decorations_config.get('elements', []))}, 纹理={texture_type}"
                from status_manager import update_feature_status
                update_feature_status("伪原创6-动态装饰效果", "成功", success_details)

                # 正式完成第六步，使用状态管理器
                status_manager.complete_step("动态装饰效果", 6, TOTAL_STEPS, success=True, details=success_details, output_file=temp_step6)

                return True
            else:
                if log_func:
                    log_func("[伪原创] Step 6/11 动态装饰应用失败")
                return False

        except Exception as e:
            if log_func:
                log_func(f"[伪原创] Step 6/11 动态装饰处理异常: {str(e)}")
            return False
    else:
        # 不应用装饰效果，直接复制文件
        try:
            import shutil
            shutil.copy2(temp_step5, temp_step6)
            if log_func:
                log_func("[伪原创] Step 6/11 未选择装饰效果")

            # 更新功能状态
            success_details = "无装饰效果应用"
            from status_manager import update_feature_status
            update_feature_status("伪原创6-动态装饰效果", "成功", success_details)

            # 正式完成第六步，使用状态管理器
            status_manager.complete_step("动态装饰效果", 6, TOTAL_STEPS, success=True, details=success_details, output_file=temp_step6)

            return True
        except Exception as e:
            if log_func:
                log_func(f"[伪原创] Step 6/11 文件复制失败: {str(e)}")
            return False


def execute_portrait_conversion(temp_step6, temp_step7, enable_landscape_to_portrait, log_func, overlay_text=None):
    """执行横屏转竖屏转换处理（包含文案添加）"""
    try:
        # 完整的横屏转竖屏处理逻辑
        import config_manager
        enable_text_overlay = config_manager.get_config().get('enable_text_overlay', True)

        # 优先使用传入的overlay_text参数，如果为None或空，则尝试从配置中获取
        if overlay_text is None or overlay_text == "":
            overlay_text = config_manager.get_config().get('overlay_text', '')
            if log_func:
                log_func(f"[伪原创7] 从配置中读取文案: {overlay_text}")

        # 获取视频分辨率
        original_width, original_height = 1920, 1080  # 默认值
        try:
            cmd_resolution = [
                'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height', '-of', 'csv=p=0:s=x', temp_step6
            ]
            result_resolution = subprocess.run(cmd_resolution, capture_output=True, text=True, encoding='utf-8', errors='replace')
            if result_resolution.returncode == 0:
                resolution_str = result_resolution.stdout.strip()
                width_str, height_str = resolution_str.split('x')
                original_width, original_height = int(width_str), int(height_str)
                if log_func:
                    log_func(f"[伪原创7] 检测到视频原始分辨率: {original_width}x{original_height}")
        except Exception as e:
            if log_func:
                log_func(f"[伪原创7] 获取视频分辨率失败: {str(e)}")

        # 生成背景色
        import colorsys
        h = random.random()
        s = random.uniform(0.18, 0.32)
        l = random.uniform(0.78, 0.92)
        r, g, b = colorsys.hls_to_rgb(h, l, s)
        bg_color = f"0x{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"

        # 设置基础滤镜
        if enable_landscape_to_portrait:
            base_filter = f"scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:{bg_color}"
            if log_func:
                log_func(f"[伪原创7] 横屏转竖屏处理，标准化为1080x1920")
        else:
            if original_height > original_width:
                base_filter = f"scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:{bg_color}"
                if log_func:
                    log_func(f"[伪原创7] 保持竖屏方向，标准化为1080x1920")
            else:
                base_filter = f"scale=-1:1080,pad=1920:1080:(ow-iw)/2:(oh-ih)/2:{bg_color}"
                if log_func:
                    log_func(f"[伪原创7] 保持横屏方向，标准化为1920x1080")

        # 添加文案处理
        if enable_text_overlay and overlay_text and overlay_text.strip():
            # 使用固定的文案颜色，避免每次变化
            text_color = "0099cc"  # 深天蓝色，降低20%亮度
            font_size = random.randint(60, 65)
            position_options = [
                {"x": "(w-text_w)/2", "y": "500"},
                {"x": "(w-text_w)/2", "y": "500"},
                {"x": "(w-text_w)/2", "y": "500"}
            ]
            text_position = random.choice(position_options)
            animation_speed = random.uniform(60.0, 120.0)
            animation_range = random.randint(3, 8)
            spaced_text = " ".join(list(overlay_text))

            # 转义文本中的特殊字符
            escaped_spaced_text = spaced_text.replace("'", "\\'").replace(":", "\\:").replace("\\", "\\\\")

            # 使用SimHei字体（黑体）
            drawtext_filter = (
                f"drawtext=font=SimHei:text={escaped_spaced_text}:fontsize={font_size}:"
                f"fontcolor=black@0.8:x={text_position['x']}+2:y={text_position['y']}+2,"
                f"drawtext=font=SimHei:text={escaped_spaced_text}:fontsize={font_size}:"
                f"fontcolor=0x{text_color}:x={text_position['x']}+{animation_range*0.5}*sin(2*PI*t/{animation_speed}):"
                f"y={text_position['y']}+{animation_range*0.25}*sin(2*PI*t/{animation_speed}+PI/4)"
            )
            
            if log_func:
                log_func(f"[伪原创7] 使用字体名称: SimHei (黑体)")
                log_func(f"[伪原创7] 完整的drawtext滤镜: {drawtext_filter}")

            vf_portrait = f"{base_filter},{drawtext_filter}"
            if log_func:
                log_func(f"[伪原创] Step 7/11 文案内容: {overlay_text}")
                log_func(f"[伪原创] Step 7/11 文案位置: {list(text_position.values())}")
                log_func(f"[伪原创] Step 7/11 文案大小: {font_size}px")
                log_func(f"[伪原创] Step 7/11 文案颜色: 0x{text_color}")
                log_func(f"[伪原创] Step 7/11 drawtext滤镜: {drawtext_filter}")
        else:
            overlay_text = ""
            vf_portrait = base_filter
            if log_func:
                if not enable_text_overlay:
                    log_func("[伪原创] Step 7/11 文案填写功能已禁用，不添加文案")
                else:
                    log_func("[伪原创] Step 7/11 未提供文案，不添加文案")

        # 执行FFmpeg命令
        cmd_final = [
            'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', temp_step6,
            '-vf', vf_portrait,
            '-c:v', 'h264_nvenc', '-pix_fmt', 'yuv420p', '-b:v', '15000k', '-preset', 'p1',
            '-c:a', 'copy', temp_step7
        ]

        if log_func:
            log_func(f"[伪原创7] Step 7/11 ffmpeg命令: {' '.join(cmd_final)}")
            log_func(f"[伪原创7] Step 7/11 视频加速/横屏转竖屏参数：\n竖屏分辨率=1080x1920\n背景色={bg_color}\npad滤镜={vf_portrait}\n文案内容={overlay_text if overlay_text and overlay_text.strip() else '无'}")

        # 使用Popen来实时监控进度
        process = subprocess.Popen(
            cmd_final,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace',
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0
        )

        # 监控进度
        last_progress = 0
        for line in process.stderr:
            if "frame=" in line and log_func:
                # 简单的进度估算
                try:
                    import re
                    time_match = re.search(r'time=(\d{2}):(\d{2}):(\d{2}\.\d{2})', line)
                    if time_match:
                        hours = int(time_match.group(1))
                        minutes = int(time_match.group(2))
                        seconds = float(time_match.group(3))
                        current_seconds = hours * 3600 + minutes * 60 + seconds

                        # 假设总时长为240秒（可以从之前步骤获取）
                        progress = min(int((current_seconds / 240) * 100), 99)
                        if progress > last_progress + 10:  # 每10%记录一次
                            last_progress = progress
                            if log_func:
                                log_func(f"[伪原创7] 进度: {progress}%")
                except Exception:
                    pass

        process.wait()

        if process.returncode == 0:
            if log_func:
                log_func("[伪原创] Step 7/11 横屏转竖屏处理完成")
                log_func(f"[伪原创7] 进度: 100%")
            return True
        else:
            if log_func:
                log_func(f"[伪原创] Step 7/11 横屏转竖屏处理失败，返回码: {process.returncode}")
            return False

    except Exception as e:
        if log_func:
            log_func(f"[伪原创7] 横屏转竖屏处理异常: {str(e)}")
        return False

def apply_speed_change(input_video, output_video, speed_factor, log_func=None):
    """
    实现第9步变速处理，随机调整视频播放速度

    处理内容：
    1. 随机选择1.03到1.05的变速倍数
    2. 同时调整视频和音频的播放速度
    3. 保持音视频同步

    参数:
    input_video: 输入视频路径
    output_video: 输出视频路径
    speed_factor: 变速倍数（1.03-1.05）
    log_func: 日志函数

    返回:
    bool: 处理是否成功
    """

    if log_func:
        log_func(f"[伪原创9] 开始变速处理: {speed_factor}x")
        log_func(f"[伪原创9] 输入文件: {input_video}")
        log_func(f"[伪原创9] 输出文件: {output_video}")

    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_video):
            if log_func:
                log_func(f"[伪原创9] 错误: 输入文件不存在 {input_video}")
            return False

        # 检查硬件加速支持
        try:
            # 检查是否支持CUDA硬件加速
            encoders_cmd = ['ffmpeg', '-encoders']
            encoders_check = subprocess.run(encoders_cmd,
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          timeout=5)

            use_hardware = False
            if encoders_check.returncode == 0:
                encoders_output = encoders_check.stdout.decode('utf-8', errors='ignore')
                if 'h264_nvenc' in encoders_output:
                    use_hardware = True
                    if log_func:
                        log_func("[伪原创9] 检测到CUDA硬件加速支持")
                else:
                    if log_func:
                        log_func("[伪原创9] 未检测到硬件加速，使用CPU处理")
            else:
                if log_func:
                    log_func("[伪原创9] 无法检查硬件加速支持，使用CPU处理")
        except Exception as e:
            use_hardware = False
            if log_func:
                log_func(f"[伪原创9] 硬件加速检测异常: {str(e)[:100]}")
                log_func("[伪原创9] 将使用CPU处理")

        # 构建变速处理命令
        # 使用setpts和atempo滤镜同时处理视频和音频
        video_pts = f"setpts={1/speed_factor:.6f}*PTS"  # 视频时间戳调整
        audio_tempo = f"atempo={speed_factor:.6f}"       # 音频速度调整

        if use_hardware:
            # 硬件加速命令
            speed_cmd = [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-y', '-i', input_video,
                '-filter_complex', f'[0:v]{video_pts}[v];[0:a]{audio_tempo}[a]',
                '-map', '[v]', '-map', '[a]',
                '-c:v', 'h264_nvenc', '-preset', 'p1', '-b:v', '15000k',
                '-c:a', 'aac', '-b:a', '128k',
                '-pix_fmt', 'yuv420p',
                output_video
            ]
        else:
            # CPU处理命令
            speed_cmd = [
                'ffmpeg', '-y', '-i', input_video,
                '-filter_complex', f'[0:v]{video_pts}[v];[0:a]{audio_tempo}[a]',
                '-map', '[v]', '-map', '[a]',
                '-c:v', 'libx264', '-preset', 'medium', '-b:v', '15000k',
                '-c:a', 'aac', '-b:a', '128k',
                '-pix_fmt', 'yuv420p',
                output_video
            ]

        if log_func:
            log_func(f"[伪原创9] 执行变速命令: {' '.join(speed_cmd)}")

        # 执行变速处理
        process = subprocess.run(speed_cmd,
                               capture_output=True,
                               text=True,
                               encoding='utf-8',
                               errors='replace')

        if process.returncode != 0:
            if log_func:
                log_func(f"[伪原创9] 变速处理失败，返回码: {process.returncode}")
                log_func(f"[伪原创9] 错误信息: {process.stderr}")
            return False

        # 验证输出文件
        if not os.path.exists(output_video):
            if log_func:
                log_func(f"[伪原创9] 错误: 输出文件未生成 {output_video}")
            return False

        # 检查输出文件大小
        output_size = os.path.getsize(output_video)
        if output_size < 1024:  # 小于1KB认为是无效文件
            if log_func:
                log_func(f"[伪原创9] 错误: 输出文件过小 {output_size} bytes")
            return False

        if log_func:
            log_func(f"[伪原创9] ✅ 变速处理成功完成")
            log_func(f"[伪原创9] 输出文件大小: {output_size / 1024 / 1024:.2f} MB")

        return True

    except Exception as e:
        if log_func:
            log_func(f"[伪原创9] 变速处理异常: {str(e)}")
        return False



